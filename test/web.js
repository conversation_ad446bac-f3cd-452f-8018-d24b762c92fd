const { expect } = require('chai');
const { assert } = require('chai');
const request = require('supertest');
const sinon = require('sinon');
const fs = require('fs');
const { app, mongoose, validImagePath, createUser, getProfile, getProfilePreview } = require('./common');
const User = require('../models/user');
const Question = require('../models/question');
const Comment = require('../models/comment');
const Interest = require('../models/interest');
const interestLib = require('../lib/interest');
const webIdLib = require('../lib/web-id');
const emailLib = require('../lib/email');
const {
  addWebIdToQuestions, createQuestion, createSitemap, createInterestSitemap
} = require('../lib/social');
const constants = require('../lib/constants');
const Category = require('../models/category');
const Subcategory = require('../models/subcategory');
const Profile = require('../models/profile');
const WebVisitor = require('../models/web-visitor');
const AppVisitor = require('../models/app-visitor');
const databaseLib = require('../lib/database');
const sitemapPaginationLib = require('../lib/sitemap-pagination');
const { initApp } = require('./helper/api');
const { getMockEmail, getMockNumber } = require('./utilities/mockuser');
const emailUnsubLib = require('../lib/email-unsub');
const SignupRejected = require('../models/signup-rejected');
const { s3 } = require('../lib/s3');
const { createStubs, fakeAdminAuth, fakeSES } = require('../test/stub');
const { getTranslatedTime, translateEmail } = require('../lib/email');
const basic = require('../lib/basic');
const Blogs = require('../models/blogs');
const LocationBlogs = require('../models/locationBlogs');
const PersonalityBlogs = require('../models/personalityBlogs');
const { createBlogsSitemaps } = require('../lib/blogs');
const locationBlogs = require('../models/locationBlogs');
const EmailSignup = require('../models/email-signup');
const DatabaseParagraph = require('../models/database-paragraph');
const SitemapPaginatedSubcategories = require('../models/sitemap-paginated-subcategories');
const SitemapPaginatedCategories = require('../models/sitemap-paginated-categories');
const SitemapPaginatedProfiles = require('../models/sitemap-paginated-profiles');
const PersonalityDatabaseChangeTracker = require('../models/personality-database-change-tracker');
const personalityDatabaseChangeHelper = require('../lib/personality-database-change-helper');
const moment = require('moment');

async function commentsUpdateDelete(profileIds) {

  await Comment.deleteMany({
    parent: { $in: profileIds },
    profile: { $in: profileIds },
    text: '',
    gif: { $exists : false },
    image: { $exists : false },
    audio: { $exists : false },
    'vote.mbti': { $exists: false },
    'vote.enneagram': { $exists: false },
    'vote.horoscope': { $exists: true },
  });

  //after delete we can just unset horoscope for remaining comments from profile

  await Comment.updateMany(
    {
      parent: { $in: profileIds },
      profile: { $in: profileIds },
    },
    {
      $unset: { 'vote.horoscope': 1 },
    }
  );
}

describe('track signup rates app-483', async () => {
  it('test kochava added and config', async () => {
    let res = await request(app)
      .put('/web/appVisitor')
      .send({ deviceId: '0', locale: 'en', kochava: { network: 'fb', partner_campaign_name: 'C. US L. English || Interest Anime || App' } });
    expect(res.status).to.equal(200);
    expect(res.body.config.app_483).to.equal(true);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({ deviceId: '0', locale: 'en' });
    expect(res.status).to.equal(200);

    let visitor = await AppVisitor.findOne({ deviceId: '0' });
    expect(visitor.events.signed_up).to.equal(true);
    expect(visitor).to.have.property('kochava');
    expect(visitor.config.app_483).to.equal();
    expect(visitor.config.app_483_v2).to.equal();

    user = await User.findById('0');
    expect(user.config.app_483).to.equal();
    expect(user.config.app_483_v2).to.equal();

    // Should not have config as not niche campaign
    res = await request(app)
      .put('/web/appVisitor')
      .send({ deviceId: '1', locale: 'en', kochava: { network: 'fb', partner_campaign_name: 'C. US L. English || App' } });
    expect(res.status).to.equal(200);
    expect(res.body.config.app_483).to.equal();

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 1)
      .send({ deviceId: '1', locale: 'en' });
    expect(res.status).to.equal(200);

    visitor = await AppVisitor.findOne({ deviceId: '1' });
    expect(visitor.events.signed_up).to.equal(true);
    expect(visitor).to.have.property('kochava');
    expect(visitor.config.app_483).to.equal();
    expect(visitor.config.app_483_v2).to.equal();

    user = await User.findById('1');
    expect(user.config.app_483).to.equal();
    expect(user.config.app_483_v2).to.equal();

    // Should not have kochava and config as kochava is not present
    res = await request(app)
      .put('/web/appVisitor')
      .send({ deviceId: '2', locale: 'en' });
    expect(res.status).to.equal(200);
    expect(res.body.config.app_483).to.equal();

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 2)
      .send({ deviceId: '2', locale: 'en' });
    expect(res.status).to.equal(200);

    visitor = await AppVisitor.findOne({ deviceId: '2' });
    expect(visitor.events.signed_up).to.equal(true);
    expect(visitor.kochava).to.equal(undefined);
    expect(visitor.config.app_483).to.equal();
    expect(visitor.config.app_483_v2).to.equal();

    // Try with gaming campaign
    // Should have config 483 undefined as not valid anime campaign
    res = await request(app)
      .put('/web/appVisitor')
      .send({ deviceId: '4', locale: 'en', kochava: { network: 'Reddit', partner_campaign_name: 'C. US L. English || Interest Game || App' } });
    expect(res.status).to.equal(200);
    expect(res.body.config.app_483).to.equal();

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 4)
      .send({ deviceId: '4', locale: 'en' });
    expect(res.status).to.equal(200);

    visitor = await AppVisitor.findOne({ deviceId: '4' });
    expect(visitor.events.signed_up).to.equal(true);
    expect(visitor).to.have.property('kochava');
    expect(visitor.config.app_483).to.equal();
    expect(visitor.config.app_483_v2).to.equal();
  });
});

describe('track signup rates app-459', async () => {
  it('test kochava added and config', async () => {
    let res = await request(app)
      .put('/web/appVisitor')
      .send({ deviceId: '0', locale: 'en', kochava: { network: 'fb', partner_campaign_name: 'C. US L. English || Interest Game || App' } });
    expect(res.status).to.equal(200);
    expect(res.body.config.app_459).to.equal(true);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({ deviceId: '0', locale: 'en' });
    expect(res.status).to.equal(200);

    let visitor = await AppVisitor.findOne({ deviceId: '0' });
    expect(visitor.events.signed_up).to.equal(true);
    expect(visitor).to.have.property('kochava');
    expect(visitor.config.app_459).to.equal();
    expect(visitor.config.app_459_v2).to.equal();

    user = await User.findById('0');
    expect(user.config.app_459).to.equal();
    expect(user.config.app_459_v2).to.equal();

    // Should not have config as not niche campaign
    res = await request(app)
      .put('/web/appVisitor')
      .send({ deviceId: '1', locale: 'en', kochava: { network: 'fb', partner_campaign_name: 'C. US L. English || App' } });
    expect(res.status).to.equal(200);
    expect(res.body.config.app_459).to.equal();

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 1)
      .send({ deviceId: '1', locale: 'en' });
    expect(res.status).to.equal(200);

    visitor = await AppVisitor.findOne({ deviceId: '1' });
    expect(visitor.events.signed_up).to.equal(true);
    expect(visitor).to.have.property('kochava');
    expect(visitor.config.app_459).to.equal();
    expect(visitor.config.app_459_v2).to.equal();

    user = await User.findById('1');
    expect(user.config.app_459).to.equal();
    expect(user.config.app_459_v2).to.equal();

    // Should not have kochava and config as kochava is not present
    res = await request(app)
      .put('/web/appVisitor')
      .send({ deviceId: '2', locale: 'en' });
    expect(res.status).to.equal(200);
    expect(res.body.config.app_459).to.equal();

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 2)
      .send({ deviceId: '2', locale: 'en' });
    expect(res.status).to.equal(200);

    visitor = await AppVisitor.findOne({ deviceId: '2' });
    expect(visitor.events.signed_up).to.equal(true);
    expect(visitor.kochava).to.equal(undefined);
    expect(visitor.config.app_459).to.equal();
    expect(visitor.config.app_459_v2).to.equal();
  });
});

describe('Send login email from backend', async () => {
  let authStub, sesStub;
  beforeEach(() => {
    authStub = sinon.stub(fakeAdminAuth, 'generateSignInWithEmailLink').callsFake((email, actionCodeSettings) => new Promise((resolve, reject) => {
      if (email === '<EMAIL>') {
        return reject(new Error('Failed sign in link generation'));
      }
      const url = new URL(actionCodeSettings.url);
      url.searchParams.append('email', email);
      url.searchParams.append('handleCodeInApp', actionCodeSettings.handleCodeInApp);
      url.searchParams.append('iOSBundleId', actionCodeSettings.iOS.bundleId);
      url.searchParams.append('androidPackageName', actionCodeSettings.android.packageName);
      resolve(url.toString());
    }));

    sesStub = sinon.stub(fakeSES, 'sendTemplatedEmail').callsFake((params) => ({
      promise: () => new Promise((resolve, reject) => {
        resolve({});
      }),
    }));
  });

  it('should email user sign in link', async () => {
    let res = await request(app)
      .put('/web/emailLogin')
      .send({ deviceId: '0', email: '<EMAIL>', timezone: 'Asia/Dhaka', locale: 'bn' });
    expect(res.status).to.equal(200);
    expect(res.body.allowed).to.equal(true);
    expect(res.body.useLegacy).to.equal(false);
    let translatedTime = getTranslatedTime('Asia/Dhaka');

    // wait 10ms for email to be sent
    await new Promise((resolve) => setTimeout(resolve, 10));

    const params = {
      email: '<EMAIL>',
      actionCodeSettings: {
        url: 'https://joinbeta.boo.dating/test',
        handleCodeInApp: true,
        iOS: {
          bundleId: 'enterprises.dating.boo',
        },
        android: {
          packageName: 'enterprises.dating.boo',
        },
      },
    };
    sinon.assert.calledWith(authStub, params.email, params.actionCodeSettings);

    const emailOptions = {
      email: '<EMAIL>',
      locale: 'bn',
      timezone: 'Asia/Dhaka',
      signInLink: 'https://joinbeta.boo.dating/test?email=test%40boo.world&handleCodeInApp=true&iOSBundleId=enterprises.dating.boo&androidPackageName=enterprises.dating.boo',
      subject: 'Sign in to Boo requested at {{time}}',
      V5_LOGIN_INTRO: `Here's Your Boo Login Link`,
      greetings: 'Hello,',
      introText: 'We received a request to sign in to Boo using this email address, at {{time}}. If you want to sign in with your {{email}} account, click this link:',
      signInText: 'Sign in to Boo',
      ignoreText: 'If you did not request this link, you can safely ignore this email.',
      V5_MOTTO: `We stand for love.`,
      COPYRIGHT: `Copyright © {{year}} Boo, all rights reserved.`,
      V5_ADDRESS: `525 3rd St Lake Oswego, Oregon 97034, USA`,
      DIR: `ltr`,
    };

    const templateData = {
      subject: translateEmail({ phrase: emailOptions.subject, locale: 'bn' }, { time: translatedTime }),
      SUBJECT: translateEmail({ phrase: emailOptions.subject, locale: 'bn' }, { time: translatedTime }),
      V5_LOGIN_INTRO: translateEmail({ phrase: emailOptions.V5_LOGIN_INTRO, locale: 'bn' }),
      greetings: translateEmail({ phrase: emailOptions.greetings, locale: 'bn' }),
      introText: translateEmail({ phrase: emailOptions.introText, locale: 'bn' }, { time: translatedTime, email: emailOptions.email }),
      signInLink: emailOptions.signInLink,
      signInText: translateEmail({ phrase: emailOptions.signInText, locale: 'bn' }).toUpperCase(),
      ignoreText: translateEmail({ phrase: emailOptions.ignoreText, locale: 'bn' }),
      V5_MOTTO: translateEmail({ phrase: emailOptions.V5_MOTTO, locale: 'bn' }),
      COPYRIGHT: translateEmail({ phrase: emailOptions.COPYRIGHT, locale: 'bn' }, { year: moment().year() }),
      V5_ADDRESS: emailOptions.V5_ADDRESS,
      DIR: emailOptions.DIR,
    };

    const sesParams = {
      Destination: { ToAddresses: ['<EMAIL>'] },
      Source: 'Boo <<EMAIL>>',
      Template: 'sign-in-v6',
      TemplateData: JSON.stringify(templateData),
      ConfigurationSetName: 'no-click-tracking',
      Tags: [{ Name: 'template', Value: 'sign-in-v6' }],
    };
    sinon.assert.calledWith(sesStub, sinon.match(sesParams));

    // Should create a record in email signup collection
    let data = await EmailSignup.findOne({ email: '<EMAIL>' });
    expect(data).to.not.equal(null);
    expect(data.signedUp).to.equal();

    // Send request to initApp
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({ appVersion: '1.13.73', email: '<EMAIL>' });
    expect(res.status).to.equal(200);

    data = await EmailSignup.findOne({ email: '<EMAIL>' });
    expect(data.signedUp).to.equal(true);
  });

  it('should check ses call for japanese email', async () => {
    let res = await request(app)
      .put('/web/emailLogin')
      .send({ deviceId: '1', email: '<EMAIL>', timezone: 'Asia/Tokyo', locale: 'ja' });
    expect(res.status).to.equal(200);
    expect(res.body.allowed).to.equal(true);
    expect(res.body.useLegacy).to.equal(false);

    const translatedTime = getTranslatedTime('Asia/Tokyo');
    await new Promise((resolve) => setTimeout(resolve, 10));

    const templateData = {
      subject: `Boo へのログインが ${translatedTime} にリクエストされました`,
      SUBJECT: `Boo へのログインが ${translatedTime} にリクエストされました`,
      V5_LOGIN_INTRO: `あなたのBooログインリンクです`,
      greetings: 'お客様',
      introText: `このメールアドレスでの Boo へのログインのリクエストを ${translatedTime} に受け付けました。<EMAIL> のアカウントでログインするには、こちらのリンクをクリックしてください。`,
      signInLink: 'https://joinbeta.boo.dating/test?email=test2%40boo.world&handleCodeInApp=true&iOSBundleId=enterprises.dating.boo&androidPackageName=enterprises.dating.boo',
      signInText: `Boo にログイン`.toUpperCase(),
      ignoreText: 'このリンクをリクエストしていない場合は、このメールを無視してください。',
      V5_MOTTO: '私たちは愛を貫きます。',
      COPYRIGHT: 'Copyright © 2025 Boo 著作権で保護されており無断複写・転載を禁じます',
      V5_ADDRESS: `525 3rd St Lake Oswego, Oregon 97034, USA`,
      DIR: `ltr`,
    };

    const sesParams = {
      ConfigurationSetName: 'no-click-tracking',
      Destination: { ToAddresses: ['<EMAIL>'] },
      Source: 'Boo <<EMAIL>>',
      Tags: [{ Name: 'template', Value: 'sign-in-v6' }],
      Template: 'sign-in-v6',
      TemplateData: JSON.stringify(templateData),
    };
    sinon.assert.calledWith(sesStub, sinon.match(sesParams));
  });

  it('should not email user sign in link when auth not allowed', async () => {
    let res = await request(app)
      .put('/web/emailLogin')
      .send({ deviceId: '0', email: '<EMAIL>', timezone: 'Asia/Dhaka', locale: 'bn' });
    expect(res.status).to.equal(200);
    expect(res.body.allowed).to.equal(false);
    expect(res.body.useLegacy).to.equal();
    sinon.assert.notCalled(authStub);
    sinon.assert.notCalled(sesStub);
  });

  it('should not email user when link generation failed', async () => {
    let res = await request(app)
      .put('/web/emailLogin')
      .send({ deviceId: '0', email: '<EMAIL>', timezone: 'Asia/Dhaka', locale: 'bn' });
    expect(res.status).to.equal(200);
    expect(res.body.allowed).to.equal(true);
    expect(res.body.useLegacy).to.equal(true);

    const params = {
      email: '<EMAIL>',
      actionCodeSettings: {
        url: 'https://joinbeta.boo.dating/test',
        handleCodeInApp: true,
        iOS: {
          bundleId: 'enterprises.dating.boo',
        },
        android: {
          packageName: 'enterprises.dating.boo',
        },
      },
    };
    sinon.assert.calledWith(authStub, params.email, params.actionCodeSettings);

    // SES should not be called
    sinon.assert.notCalled(sesStub);
  });
});

it('web routes', async () => {
  // create user 0 and user 1
  for (let i = 0; i < 2; i++) {
    await createUser(i);
  }

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  kpopId = res.body.interests[0]._id;
  latinId = res.body.interests[1]._id;
  chessId = res.body.interests[2]._id;
  const { interests } = res.body;

  // user 0 posts question
  res = await request(app)
    .post('/v1/question')
    .set('authorization', 0)
    .send({
      interestId: kpopId,
      title: 'title1',
      text: 'text1',
    });
  expect(res.status).to.equal(200);
  const q1Id = res.body._id;
  const q1 = {
    _id: q1Id,
    webId: res.body.webId,
    url: res.body.url,
    createdAt: res.body.createdAt,
    profilePreview: getProfilePreview(0, true),
    interest: interests[0],
    interestName: 'kpop',
    title: 'title1',
    text: 'text1',
    numComments: 0,
    numLikes: 0,
    isDeleted: false,
    isEdited: false,
    hasUserLiked: false,
    hasUserSaved: false,
    language: 'en',
    linkedKeywords: [],
    linkedExploreKeywords: [],
    linkedPillarKeywords: [],
    linkedCategories: [],
    linkedSubcategories: [],
    linkedProfiles: [],
    hashtags: ['kpop'],
    images:[]
  };
  expect(q1.webId.length).to.equal(6);

  // user 1 posts comment
  res = await request(app)
    .post('/v1/comment')
    .set('authorization', 1)
    .send({
      questionId: q1Id,
      text: 'comment1',
      parentId: q1Id,
    });
  expect(res.status).to.equal(200);
  const c1Id = res.body._id;
  const c1 = {
    _id: c1Id,
    createdAt: res.body.createdAt,
    profilePreview: getProfilePreview(1, true),
    interestName: 'kpop',
    language: 'en',
    question: q1Id,
    text: 'comment1',
    parent: q1Id,
    repliedTo: null,
    depth: 1,
    numComments: 0,
    numLikes: 0,
    isDeleted: false,
    isEdited: false,
    hasUserLiked: false,
    isFriendComment: false,
    comments: [],
    postRepliedTo: q1Id,
    linkedKeywords: [],
    linkedExploreKeywords: [],
    linkedPillarKeywords: [],
    linkedCategories: [],
    linkedSubcategories: [],
    linkedProfiles: [],
  };
  q1.numComments += 1;

  // ***** web routes ********
  res = await request(app)
    .get('/web/interests');
  expect(res.status).to.equal(200);
  expect(res.body.interests.length).to.equal(3);
  expect(res.body.interests).to.eql(interests);

  q1.profilePreview.karma = 1;
  res = await request(app)
    .get('/web/question/feed');
  expect(res.status).to.equal(200);
  expect(res.body.questions.length).to.equal(1);
  expect(res.body.questions[0]).to.eql(q1);
  expect(res.body.exhausted).to.equal();

  res = await request(app)
    .get('/web/question/feed')
    .query({ language: 'de' })
  expect(res.status).to.equal(200);
  expect(res.body.questions.length).to.equal(0);

  res = await request(app)
    .get('/web/question/allQuestions')
    .query({ interestId: kpopId });
  expect(res.status).to.equal(200);
  expect(res.body.questions.length).to.equal(1);
  expect(res.body.questions[0]).to.eql(q1);
  expect(res.body.exhausted).to.equal();

  res = await request(app)
    .get('/web/question')
    .query({ questionId: q1Id });
  expect(res.status).to.equal(200);
  expect(res.body.question).to.eql(q1);

  res = await request(app)
    .get('/web/question')
    .query({
      interestId: kpopId,
      webId: q1.webId,
    });
  expect(res.status).to.equal(200);
  expect(res.body.question).to.eql(q1);

  res = await request(app)
    .get('/web/question')
    .query({
      interestName: 'kpop',
      webId: q1.webId,
    });
  expect(res.status).to.equal(200);
  expect(res.body.question).to.eql(q1);

  res = await request(app)
    .get('/web/comment')
    .query({ parentId: q1Id });
  expect(res.status).to.equal(200);
  expect(res.body.comments.length).to.equal(1);
  expect(res.body.comments[0]).to.eql(c1);
  expect(res.body.exhausted).to.equal();

  res = await request(app)
    .get('/web/comment/context')
    .query({ commentId: c1Id, parentId: q1Id, questionId: q1Id });
  expect(res.status).to.equal(200);
  expect(res.body.comments.length).to.equal(1);
  expect(res.body.comments[0]).to.eql(c1);

  res = await request(app)
    .get('/web/comment/context')
    .query({ commentId: 'invalid', parentId: q1Id, questionId: q1Id });
  expect(res.status).to.equal(422);

  // user 1 is banned
  user = await User.findOne({ _id: 1 });
  user.shadowBanned = true;
  await user.save();

  res = await request(app)
    .get('/web/question/feed');
  expect(res.status).to.equal(200);
  expect(res.body.questions.length).to.equal(1);
  expect(res.body.questions[0]).to.eql(q1);

  res = await request(app)
    .get('/web/question')
    .query({ questionId: q1Id });
  expect(res.status).to.equal(200);
  expect(res.body.question).to.eql(q1);

  res = await request(app)
    .get('/web/question')
    .query({
      interestId: kpopId,
      webId: q1.webId,
    });
  expect(res.status).to.equal(200);
  expect(res.body.question).to.eql(q1);

  res = await request(app)
    .get('/web/comment')
    .query({ parentId: q1Id });
  expect(res.status).to.equal(200);
  expect(res.body.comments.length).to.equal(0);

  // user 0 is banned
  user = await User.findOne({ _id: 0 });
  user.shadowBanned = true;
  await user.save();

  res = await request(app)
    .get('/web/question/feed');
  expect(res.status).to.equal(200);
  expect(res.body.questions.length).to.equal(0);

  res = await request(app)
    .get('/web/question')
    .query({ questionId: q1Id });
  expect(res.status).to.equal(404);

  res = await request(app)
    .get('/web/question')
    .query({
      interestId: kpopId,
      webId: q1.webId,
    });
  expect(res.status).to.equal(404);

  res = await request(app)
    .get('/web/comment')
    .query({ parentId: q1Id });
  expect(res.status).to.equal(200);
  expect(res.body.comments.length).to.equal(0);
});

it('question internal Linking', async () => {
  for (let i = 0; i < 2; i++) {
    await createUser(i);
  }

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  kpopId = res.body.interests[0]._id;

  let questionId = null
  let createdAt  = null
  let firstQuestionId = null
  let secondQuestionId = null
  let fifthQuestionId = null
  for (let i = 0; i < 10; i++) {
    res = await request(app)
    .post('/v1/question')
    .set('authorization', i < 6 ? 0 : 1)
    .send({
      interestId: kpopId,
      title: `Title No - ${i}`,
      text: `Text No - ${i}`,
    });
    if(i == 0) firstQuestionId = res.body._id
    if(i == 1) secondQuestionId = res.body._id
    if(i == 5) fifthQuestionId = res.body._id
    questionId = res.body._id
    createdAt = res.body.createdAt
  }

  res = await request(app) // no query pass check
    .get('/web/question/internalLinking')
  expect(res.status).to.equal(404);

  res = await request(app) // no previous question before this
    .get('/web/question/internalLinking')
    .query({ questionId: firstQuestionId })
  expect(res.status).to.equal(200)
  expect(res.body.questions.length).to.be.equal(0)

  res = await request(app) // one previous question before this question
  .get('/web/question/internalLinking')
  .query({ questionId: secondQuestionId })
  expect(res.status).to.equal(200)
  expect(res.body.questions.length).to.be.equal(1)
  expect(res.body.questions[0].title).to.equal('Title No - 0')

  res = await request(app) // invalid query id
  .get('/web/question/internalLinking')
  .query({ questionId: '123123afb21312' })
  expect(res.status).to.equal(422);

  res = await request(app) //success case
    .get('/web/question/internalLinking')
    .query({ questionId: questionId })

  expect(res.status).to.equal(200);
  expect(res.body.questions.length).to.be.below(4)
  expect(res.body.questions[0].interestName).to.be.equal('kpop')
  expect(res.body.questions[0].language).to.be.equal('en')
  expect(res.body.questions[0].title).to.equal('Title No - 8')
  expect(new Date(res.body.questions[0].createdAt)).to.be.below(new Date(createdAt))

  u = await User.findById('1');
  u.shadowBanned = true;
  await u.save();

  res = await request(app) //success case after shadowBanned user 1
    .get('/web/question/internalLinking')
    .query({ questionId: questionId })

  expect(res.status).to.equal(200);
  expect(res.body.questions.length).to.be.below(4)
  expect(res.body.questions[0].interestName).to.be.equal('kpop')
  expect(res.body.questions[0].language).to.be.equal('en')
  expect(res.body.questions[0].title).to.equal('Title No - 5')
  expect(new Date(res.body.questions[0].createdAt)).to.be.below(new Date(createdAt))

  q = await Question.findById(fifthQuestionId);
  q.mediaUploadPending = true;
  await q.save();

  res = await request(app) //success case after mediaUploadPending set to true
  .get('/web/question/internalLinking')
  .query({ questionId: questionId })

  expect(res.status).to.equal(200);
  expect(res.body.questions.length).to.be.below(4)
  expect(res.body.questions[0].interestName).to.be.equal('kpop')
  expect(res.body.questions[0].language).to.be.equal('en')
  expect(res.body.questions[0].title).to.equal('Title No - 4')
  expect(new Date(res.body.questions[0].createdAt)).to.be.below(new Date(createdAt))

})

it('web id collision - retry failure', async () => {
  await Question.ensureIndexes();

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  kpopId = res.body.interests[0]._id;

  // set up stub to always return same value
  sinon
    .stub(webIdLib, 'getWebId')
    .resolves('1');

  // first post should succeed
  res = await request(app)
    .post('/v1/question')
    .set('authorization', 0)
    .send({
      interestId: kpopId,
      title: 'title1',
      text: 'text1',
    });
  expect(res.status).to.equal(200);

  // second post should fail
  res = await request(app)
    .post('/v1/question')
    .set('authorization', 0)
    .send({
      interestId: kpopId,
      title: 'title2',
      text: 'text2',
    });
  expect(res.status).to.equal(500);

  // only one question saved
  res = await request(app)
    .get('/web/question/feed');
  expect(res.status).to.equal(200);
  expect(res.body.questions.length).to.equal(1);
  expect(res.body.questions[0].webId).to.equal('1');

  // restore stub
  webIdLib.getWebId.restore();
});

it('web id collision - retry success', async () => {
  await Question.ensureIndexes();

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  kpopId = res.body.interests[0]._id;

  // set up stub to return same value, then different value
  sinon
    .stub(webIdLib, 'getWebId')
    .onCall(0).resolves('1')
    .onCall(1)
    .resolves('1')
    .onCall(2)
    .resolves('2');

  // first post should succeed
  res = await request(app)
    .post('/v1/question')
    .set('authorization', 0)
    .send({
      interestId: kpopId,
      title: 'title1',
      text: 'text1',
    });
  expect(res.status).to.equal(200);

  // second post should succeed
  res = await request(app)
    .post('/v1/question')
    .set('authorization', 0)
    .send({
      interestId: kpopId,
      title: 'title2',
      text: 'text2',
    });
  expect(res.status).to.equal(200);

  // two questions saved
  res = await request(app)
    .get('/web/question/feed');
  expect(res.status).to.equal(200);
  expect(res.body.questions.length).to.equal(2);
  expect(res.body.questions[0].webId).to.equal('2');
  expect(res.body.questions[1].webId).to.equal('1');

  // restore stub
  webIdLib.getWebId.restore();
});

it('web id collision - different parents', async () => {
  await Question.ensureIndexes();

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  kpopId = res.body.interests[0]._id;
  latinId = res.body.interests[1]._id;

  // set up stub to always return same value
  sinon
    .stub(webIdLib, 'getWebId')
    .resolves('1');

  // first post to kpop should succeed
  res = await request(app)
    .post('/v1/question')
    .set('authorization', 0)
    .send({
      interestId: kpopId,
      title: 'title1',
      text: 'text1',
    });
  expect(res.status).to.equal(200);

  // second post to latin should also succeed
  res = await request(app)
    .post('/v1/question')
    .set('authorization', 0)
    .send({
      interestId: latinId,
      title: 'title2',
      text: 'text2',
    });
  expect(res.status).to.equal(200);

  // two questions saved
  res = await request(app)
    .get('/web/question/feed');
  expect(res.status).to.equal(200);
  expect(res.body.questions.length).to.equal(2);
  expect(res.body.questions[0].webId).to.equal('1');
  expect(res.body.questions[1].webId).to.equal('1');

  // restore stub
  webIdLib.getWebId.restore();
});

it('web id collision - different parents', async () => {
  await Question.ensureIndexes();

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  kpopId = res.body.interests[0]._id;
  latinId = res.body.interests[1]._id;

  // set up stub to always return same value
  sinon
    .stub(webIdLib, 'getWebId')
    .resolves('1');

  // first post to kpop should succeed
  res = await request(app)
    .post('/v1/question')
    .set('authorization', 0)
    .send({
      interestId: kpopId,
      title: 'title1',
      text: 'text1',
    });
  expect(res.status).to.equal(200);

  // second post to latin should also succeed
  res = await request(app)
    .post('/v1/question')
    .set('authorization', 0)
    .send({
      interestId: latinId,
      title: 'title2',
      text: 'text2',
    });
  expect(res.status).to.equal(200);

  // two questions saved
  res = await request(app)
    .get('/web/question/feed');
  expect(res.status).to.equal(200);
  expect(res.body.questions.length).to.equal(2);
  expect(res.body.questions[0].webId).to.equal('1');
  expect(res.body.questions[1].webId).to.equal('1');

  // restore stub
  webIdLib.getWebId.restore();
});

it('qod', async () => {
  await createQuestion({
    text: 'question 1',
    interestName: 'questions',
  });

  res = await request(app)
    .get('/web/question/feed');
  expect(res.status).to.equal(200);
  expect(res.body.questions.length).to.equal(1);
  const q1 = res.body.questions[0];

  res = await request(app)
    .get('/web/question')
    .query({
      interestId: null,
      webId: q1.webId,
    });
  expect(res.status).to.equal(200);
  expect(res.body.question).to.eql(q1);

  res = await request(app)
    .get('/web/question')
    .query({
      interestName: 'questions',
      webId: q1.webId,
    });
  expect(res.status).to.equal(200);
  expect(res.body.question).to.eql(q1);
});

it('invalid params', async () => {
  await createQuestion({
    text: 'question 1',
  });

  res = await request(app)
    .get('/web/question')
    .query({
      interestId: 'abc',
      webId: 'abc',
    });
  expect(res.status).to.equal(404);

  res = await request(app)
    .get('/web/question')
    .query({
      interestName: 'abc',
      webId: 'abc',
    });
  expect(res.status).to.equal(404);

  res = await request(app)
    .get('/web/question')
    .query({
      interestId: null,
      webId: { $gt: '' },
    });
  expect(res.status).to.equal(404);
});

it('exhausted', async () => {
  // larger page size for web
  const pageSize = constants.pageSize * 2;

  for (let i = 0; i < pageSize * 2; i++) {
    await createQuestion({
      text: i,
      interestName: 'questions',
    });
  }

  // get first page
  res = await request(app)
    .get('/web/question/allQuestions')
    .query({ interestId: null });
  expect(res.status).to.equal(200);
  expect(res.body.questions.length).to.equal(pageSize);
  expect(res.body.exhausted).to.equal();

  // get second page
  beforeId = res.body.questions[pageSize - 1]._id;
  res = await request(app)
    .get('/web/question/allQuestions')
    .query({ interestId: null, beforeId });
  expect(res.status).to.equal(200);
  expect(res.body.exhausted).to.equal();

  // try get third page - no more
  beforeId = res.body.questions[res.body.questions.length - 1]._id;
  res = await request(app)
    .get('/web/question/allQuestions')
    .query({ interestId: null, beforeId });
  expect(res.status).to.equal(200);
  expect(res.body.questions.length).to.equal(0);
});

it('error handling', async () => {
  res = await request(app)
    .get('/web/question/allQuestions')
    .query({ interestName: 'kpop', beforeId: '6326' });
  expect(res.status).to.equal(200);
  expect(res.body.questions.length).to.equal(0);
});

it('blogs sitemaps', async () => {
  let LastModifiedDate = new Date('2024-12-12')
  await PersonalityBlogs.insertMany([
    {
      url: "/personality/purposeful-dating",
      lastUpdated: LastModifiedDate,
      previews: {
        en: {
          image: "purposeful-dating.webp",
          altText: "Purposeful dating with Boo",
          title: "Eesmärgipärane dateerimine: kuidas suhteid kavatsusega hallata",
          images: [
            "purposeful-dating100.webp",
            "purposeful-dating200.webp",
            "purposeful-dating300.webp",
          ],
        },
        et: {
          image: "/purposeful-dating.webp",
          altText: "Purposeful dating with Boo",
          title: "Eesmärgipärane dateerimine: kuidas suhteid kavatsusega hallata"
        },
        fr: {
          image: "",
          altText: "Purposeful dating with Boo",
          title: "Eesmärgipärane dateerimine: kuidas suhteid kavatsusega hallata"
        },
        es: {
          altText: "Purposeful dating with Boo",
          title: "Eesmärgipärane dateerimine: kuidas suhteid kavatsusega hallata"
        }
      }
    },
    {
      url: "/resources/personality/purposeful-dating",
      previews: {
        fr: {
          image: "purposeful-dating.webp",
          altText: "Purposeful dating with Boo",
          title: "Eesmärgipärane dateerimine: kuidas suhteid kavatsusega hallata"
        },
        et: {
          image: "/purposeful-dating.webp",
          altText: "Purposeful dating with Boo",
          title: "Eesmärgipärane dateerimine: kuidas suhteid kavatsusega hallata"
        }
      }
    },
  ]);

  await Blogs.insertMany([
    {
      url: "/blogs/purposeful-dating",
      previews: {
        en: {
          image: "purposeful-dating.webp",
          altText: "Purposeful dating with Boo",
          title: "Eesmärgipärane dateerimine: kuidas suhteid kavatsusega hallata",
          images: [
            "purposeful-dating.webp",
            "purposeful-dating2.webp",
            "purposeful-dating3.webp",
          ],
        },
        et: {
          image: "/purposeful-dating.webp",
          altText: "Purposeful dating with Boo",
          title: "Eesmärgipärane dateerimine: kuidas suhteid kavatsusega hallata"
        }
      }
    },
    {
      url: "/resources/blogs/purposeful-dating",
      lastUpdated: LastModifiedDate,
      previews: {
        fr: {
          image: "purposeful-dating.webp",
          altText: "Purposeful dating with Boo",
          title: "Eesmärgipärane dateerimine: kuidas suhteid kavatsusega hallata"
        },
        et: {
          image: "/purposeful-dating.webp",
          altText: "Purposeful dating with Boo",
          title: "Eesmärgipärane dateerimine: kuidas suhteid kavatsusega hallata"
        },
        en: {
          image: "purposeful-dating.webp",
          altText: "Purposeful dating with Boo",
          title: "Eesmärgipärane dateerimine: kuidas suhteid kavatsusega hallata",
          images: [
            "purposeful-dating10.webp",
            "purposeful-dating20.webp",
            "purposeful-dating30.webp",
          ],
        }
      }
    },
  ]);

  await locationBlogs.insertMany([
    {
      url: "/location/purposeful-dating",
      lastUpdated: LastModifiedDate,
      previews: {
        en: {
          image: "purposeful-dating.webp",
          altText: "Purposeful dating with Boo",
          title: "Eesmärgipärane dateerimine: kuidas suhteid kavatsusega hallata"
        },
        et: {
          image: "/purposeful-dating.webp",
          altText: "Purposeful dating with Boo",
          title: "Eesmärgipärane dateerimine: kuidas suhteid kavatsusega hallata"
        }
      }
    },
    {
      url: "/resources/location/purposeful-dating",
      lastUpdated: LastModifiedDate,
      previews: {
        fr: {
          image: "purposeful-dating.webp",
          altText: "Purposeful dating with Boo",
          title: "Eesmärgipärane dateerimine: kuidas suhteid kavatsusega hallata"
        },
        et: {
          image: "/purposeful-dating.webp",
          altText: "Purposeful dating with Boo",
          title: "Eesmärgipärane dateerimine: kuidas suhteid kavatsusega hallata"
        }
      }
    },
  ]);

  const { resourceUrls, pillarUrls } = await createBlogsSitemaps();

  expect(resourceUrls).to.eql([
    {
      url: 'https://boo.world/fr/resources/blogs/purposeful-dating',
      lastModified: LastModifiedDate,
      image: 'https://boo-media.b-cdn.net/blogs/purposeful-dating.webp'
    },
    {
      url: 'https://boo.world/et/resources/blogs/purposeful-dating',
      lastModified: LastModifiedDate,
      image: 'https://boo-media.b-cdn.net/blogs/purposeful-dating.webp'
    },
    {
      url: 'https://boo.world/resources/blogs/purposeful-dating',
      lastModified: LastModifiedDate,
      image: [
        'https://boo-media.b-cdn.net/blogs/purposeful-dating10.webp',
        'https://boo-media.b-cdn.net/blogs/purposeful-dating20.webp',
        'https://boo-media.b-cdn.net/blogs/purposeful-dating30.webp',
      ]
    },
    {
      url: 'https://boo.world/fr/resources/personality/purposeful-dating',
      image: 'https://boo.world/personalities/blogs/purposeful-dating.webp'
    },
    {
      url: 'https://boo.world/et/resources/personality/purposeful-dating',
      image: 'https://boo.world/personalities/blogs/purposeful-dating.webp'
    },
    {
      url: 'https://boo.world/fr/resources/location/purposeful-dating',
      lastModified: LastModifiedDate,
    },
    {
      url: 'https://boo.world/et/resources/location/purposeful-dating',
      lastModified: LastModifiedDate,
     }
  ]);

  expect(pillarUrls).to.eql([
    {
      url: 'https://boo.world/blogs/purposeful-dating',
      image: 'https://boo-media.b-cdn.net/blogs/purposeful-dating.webp',
      image: [
        'https://boo-media.b-cdn.net/blogs/purposeful-dating.webp',
        'https://boo-media.b-cdn.net/blogs/purposeful-dating2.webp',
        'https://boo-media.b-cdn.net/blogs/purposeful-dating3.webp',
      ]
    },
    {
      url: 'https://boo.world/et/blogs/purposeful-dating',
      image: 'https://boo-media.b-cdn.net/blogs/purposeful-dating.webp'
    },
    {
      url: 'https://boo.world/personality/purposeful-dating',
      image: [
        'https://boo.world/personalities/blogs/purposeful-dating100.webp',
        'https://boo.world/personalities/blogs/purposeful-dating200.webp',
        'https://boo.world/personalities/blogs/purposeful-dating300.webp',
      ],
      lastModified: LastModifiedDate
    },
    {
      url: 'https://boo.world/et/personality/purposeful-dating',
      lastModified: LastModifiedDate,
      image: 'https://boo.world/personalities/blogs/purposeful-dating.webp'
    },
    {
      url: 'https://boo.world/fr/personality/purposeful-dating',
      lastModified: LastModifiedDate,
    },
    {
      url: 'https://boo.world/es/personality/purposeful-dating',
      lastModified: LastModifiedDate,
     },
    {
      url: 'https://boo.world/location/purposeful-dating',
      lastModified: LastModifiedDate,
    },
    {
      url: 'https://boo.world/et/location/purposeful-dating',
      lastModified: LastModifiedDate,
    }
  ]);

  const res = await request(app)
    .post('/v1/worker/updateSitemap')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
});


it('social sitemap', async () => {
  const now = Date.now();
  const q0 = await createQuestion({
    text: '0', interestName: 'questions', createdAt: now, numComments: 5,
  });
  const q1 = await createQuestion({
    title: 'title', text: '1', interestName: 'questions', createdAt: now + 1, numComments: 6, language: 'en'
  });
  const q2 = await createQuestion({ text: '2', interestName: 'questions', createdAt: new Date(9999, 5, 20) });
  const q3 = await createQuestion({ text: '3', banned: true });
  const q4 = await createQuestion({ text: '4', interestName: 'kpop', createdAt: now });
  const q5 = await createQuestion({
    title: '🤣', text: '', interestName: 'kpop', createdAt: now + 2, numComments: 6, language: 'de'
  });
  const q6 = await createQuestion({
    title: 'title', text: '6', interestName: 'questions', language:'iw', createdAt: now, numComments: 7,
  })

  const q7 = await createQuestion({
    text: '7', interestName: 'questions', language:'zh', createdAt: now, numComments: 8,
  })

  const q8 = await createQuestion({
    text: '8', interestName: 'questions', language:'fr', createdAt: now, numComments: 8,
  })

  const q9 = await createQuestion({
    title: '🤣', text: '', interestName: 'kpop', language:'zh', createdAt: now, numComments: 9,
  })

  const q10 = await createQuestion({
    title: '🤣', text: '', interestName: 'kpop', language:'zu', createdAt: now, numComments: 9, //to test language into in lib/translate.js
  })

  const q11 = await createQuestion({
    title: '🤣', interestName: 'kpop', language:'zu', createdAt: now, numComments: 9, //to test language into in lib/translate.js
  })

  await Interest.updateOne({name: 'kpop'}, {numQuestions: 5});

  const domain = 'https://boo.world';
  const url0 = `${domain}/u/questions/${q0.webId}/0`;
  const url1 = `${domain}/u/questions/${q1.webId}/title-1`;
  const url2 = `${domain}/de/u/kpop/${q5.webId}`;
  const url6 = `${domain}/he/u/questions/${q6.webId}/title-6`;
  const url7 = `${domain}/zh-Hans/u/questions/${q7.webId}/7`;
  const url8 = `${domain}/fr/u/questions/${q8.webId}/8`;
  const url9 = `${domain}/zh-Hans/u/kpop/${q9.webId}`;
  const url10 = `${domain}/zu/u/kpop/${q10.webId}`;
  const url11 = `${domain}/zu/u/kpop/${q11.webId}`;

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0);
  expect(res.status).to.equal(200);

  res = await request(app)
    .post('/v1/comment')
    .set('authorization', 0)
    .send({
      parentId: q5._id,
      questionId: q5._id,
      text: 'this is test comment'
    });
    expect(res.status).to.equal(200);

  let sitemap = await createSitemap();
  let questionInfo = await Question.findOne({ webId: q5.webId })
  expect(sitemap.map( (x) => x.url )).to.eql([url2, url1, url0, url6, url7, url8, url9, url10, url11]);
  expect(sitemap[0].lastModified).to.be.a('date');
  expect(new Date(sitemap[0].lastModified)).to.gte(new Date(questionInfo.createdAt));
  expect(new Date(sitemap[0].lastModified)).to.eql(new Date(questionInfo.lastCommentAddedTime));

  let interest = await Interest.findOne({ name: 'kpop' })
  sitemap = await createInterestSitemap();
  expect(sitemap[0].url).to.eql(`${domain}/u/kpop`);
  expect(sitemap[0].lastModified).to.be.a('date');
  expect(new Date(sitemap[0].lastModified)).to.gte(new Date(interest.createdAt));
  expect(new Date(sitemap[0].lastModified)).to.eql(new Date(interest.lastPostAddedTime));


  res = await request(app)
    .post('/v1/worker/updateSitemap')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
});

it('restrict shadow ban posts from sitemap', async () => {
  await createUser(0);
  await createUser(1)

  const now = Date.now();
  const q0 = await createQuestion({
    text: '0', interestName: 'questions', createdAt: now, numComments: 5
  });
  const q1 = await createQuestion({
    title: 'title', text: '1', interestName: 'questions', createdAt: now + 1, numComments: 6, language: 'en', createdBy: 0
  });
  const q2 = await createQuestion({
    title: '🤣', text: '', interestName: 'kpop', createdAt: now + 2, numComments: 6, language: 'de', createdBy: 1
  });

  const domain = 'https://boo.world';
  const url0 = `${domain}/u/questions/${q0.webId}/0`;
  const url1 = `${domain}/u/questions/${q1.webId}/title-1`;
  const url2 = `${domain}/de/u/kpop/${q2.webId}`;

  let sitemap = await createSitemap();
  expect(sitemap.map( (x) => x.url )).to.eql([url2, url1, url0]);

  await User.findOneAndUpdate({ _id: 1 }, { $set: { shadowBanned: true }})

  sitemap = await createSitemap();
  expect(sitemap.map( (x) => x.url )).to.eql([url1, url0]);
});

it('handle search', async () => {
  await createUser(0);

  res = await request(app)
    .get('/web/boo')
    .query({ handle: 'handle0' });
  expect(res.status).to.equal(200);
  expect(res.body.user).to.eql(getProfile(0));
});

it('url slugs', async () => {
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0);
  expect(res.status).to.equal(200);

  // [ title, text, slug ]
  const data = [
    ['favorite boat?', '', 'favorite-boat'],
    ['favorite-boat', '', 'favorite-boat'],
    ['favorite', 'boat?', 'favorite-boat'],
    ['What is your favorite boat?', 'please tell', 'what-is-your-favorite-boat'],
    ['favorite boat?', 'please tell what is your favorite boat', 'favorite-boat-please-tell-what-is-your-favorite'],
    ['How 「えい」 should be pronounced in the words like 英語, 先生, etc?', '', 'how-%E3%81%88%E3%81%84-should-be-pronounced-in-the-words-like-%E8%8B%B1%E8%AA%9E'],
    ['my - dog', '', 'my-dog'],
  ];

  for (const d of data) {
    res = await request(app)
      .post('/v1/question')
      .set('authorization', 0)
      .send({
        interestName: 'kpop',
        title: d[0],
        text: d[1],
      });
    expect(res.status).to.equal(200);
    const q = res.body;
    expect(q.url).to.equal(`https://boo.world/u/kpop/${q.webId}/${d[2]}`);
  }

  {
    const interests = [
      {
        category: 'Music', interest: '#r&b', name: 'r&b', sortIndex: 4,
      },
    ];
    await Interest.insertMany(interests);
    await interestLib.loadInterestsFromDatabase();

    // test interest name with &
    res = await request(app)
      .post('/v1/question')
      .set('authorization', 0)
      .send({
        interestName: 'r&b',
        title: 'title',
        text: 'text',
      });
    expect(res.status).to.equal(200);
    const q = res.body;
    expect(q.url).to.equal(`https://boo.world/u/r%26b/${q.webId}/title-text`);
  }
});

it('handle search', async () => {
  await createUser(0);

  res = await request(app)
    .get('/web/boo')
    .query({ handle: 'handle0' });
  expect(res.status).to.equal(200);
  expect(res.body.user).to.eql(getProfile(0));
});

describe('database', async () => {
  beforeEach(async () => {
    sinon.stub(databaseLib, 'getSubcategoryPageSize').returns(2);
    sinon.stub(databaseLib, 'getProfilePageSize').returns(2);

    const categories = [
      {
        id: 3, sort: 1, name: 'Anime', slug: 'anime',translatedLanguages:['en','es']
      },
      {
        id: 4, sort: 2, name: 'TV', slug: 'tv',translatedLanguages:['es']
      },
    ];
    await Category.insertMany(categories);

    const subcategories = [
      {
        id: 1, sort: 1, name: 'Naruto', slug: 'naruto', category: 3,
      },
      {
        id: 2, sort: 2, name: 'Bleach', slug: 'bleach', category: 3,
      },
      {
        id: 3, sort: 3, name: 'Pokemon', slug: 'pokemon', category: 3,translatedLanguages:['es']
      },
      {
        id: 4, sort: 4, name: 'Manifest', slug: 'manifest', category: 4,translatedLanguages:['en','fr','es']
      },
    ];
    await Subcategory.insertMany(subcategories);

    const profiles = [
      {
        id: 1, name: 'Naruto', mbti: 'ESTJ', subcategories: [1], enneagram: '1w9', horoscope: 'Aries',translatedLanguages:['fr'], continents: ['Asia', 'North America'], countries: ['Nepal', 'India', 'Haiti' ]
      },
      {
        id: 2, name: 'Sasuke Uchiha', mbti: 'ENTP', subcategories: [1], enneagram: '1w2', horoscope: 'Taurus',
      },
      {
        id: 3, name: 'Sakura', mbti: 'ESTJ', subcategories: [1], image: 'sakura.jpg',
      },
      {
        id: 4, name: 'Bleach', mbti: 'ENTJ', subcategories: [2], enneagram: '1w9', horoscope: 'Aries',
      },
      {
        id: 5, name: 'Pikachu', mbti: 'ENTP', subcategories: [3], enneagram: '1w2', horoscope: 'Taurus', image: 'pikachu.jpg',
      },
      {
        id: 6, name: 'Manifest', mbti: 'ENTP', enneagram: '1w2', subcategories: [4], continents: ['Africa'], countries: ['Chad'],
      },
    ];
    for (let index = 0; index < profiles.length; index++) {
      const newProfile = new Profile( profiles[index]);
      newProfile.lastUpdated = new Date('2024-12-12')
      await newProfile.save();
    }
    await Profile.updateMany({}, { $set: { lastUpdated: new Date('2024-12-12') }})
    await databaseLib.downloadCategoriesToFiles();

    await databaseLib.loadCategoriesFromDatabase();
  });

  it('sitemaps', async () => {

    const subcategories = [
      {
        id: 1000, sort: 4, name: 'Manifest', slug: 'manifest', category: 1000, translatedLanguages:['en','fr','es']
      },
      {
        id: 1001, sort: 4, name: 'Manifest', slug: 'manifest', category: 2000, translatedLanguages:['en','fr','es']
      },
    ];
    await Subcategory.insertMany(subcategories);

    await databaseLib.addCategoryToProfiles()

    res = await request(app)
      .post('/v1/worker/updateCountryFilterSitemaps')
    expect(res.status).to.equal(200);

    const {
      homeUrls,
      categoryUrls,
      subcategoryUrls,
      profileUrls,
    } = await databaseLib.createDatabaseSitemapsV2();

    // console.log({homeUrls:JSON.stringify(homeUrls, null, 2),categoryUrls: JSON.stringify(categoryUrls, null, 2), subcategoryUrls, profileUrls});

    expect(homeUrls.length).to.eql(36045);
    const homeUrlsSliced = homeUrls.slice(0, 400);
    expect(homeUrlsSliced).to.eql([
      "https://boo.world/database/",
      "https://boo.world/database/profiles",
      "https://boo.world/database/famous-people-personality-types",
      "https://boo.world/database/fictional-characters-personality-types",
      "https://boo.world/database/introverts",
      "https://boo.world/database/extroverts",
      "https://boo.world/database/africa",
      "https://boo.world/database/asia",
      "https://boo.world/database/europe",
      "https://boo.world/database/north-america",
      "https://boo.world/database/oceania",
      "https://boo.world/database/south-america",
      "https://boo.world/database/algeria",
      "https://boo.world/database/angola",
      "https://boo.world/database/benin",
      "https://boo.world/database/botswana",
      "https://boo.world/database/burkina-faso",
      "https://boo.world/database/burundi",
      "https://boo.world/database/cameroon",
      "https://boo.world/database/cape-verde",
      "https://boo.world/database/central-african-republic",
      "https://boo.world/database/chad",
      "https://boo.world/database/comoros",
      "https://boo.world/database/congo-republic-and-drc",
      "https://boo.world/database/côte-divoire",
      "https://boo.world/database/djibouti",
      "https://boo.world/database/egypt",
      "https://boo.world/database/equatorial-guinea",
      "https://boo.world/database/eritrea",
      "https://boo.world/database/eswatini",
      "https://boo.world/database/ethiopia",
      "https://boo.world/database/gabon",
      "https://boo.world/database/gambia",
      "https://boo.world/database/ghana",
      "https://boo.world/database/guinea",
      "https://boo.world/database/guinea-bissau",
      "https://boo.world/database/kenya",
      "https://boo.world/database/lesotho",
      "https://boo.world/database/liberia",
      "https://boo.world/database/libya",
      "https://boo.world/database/madagascar",
      "https://boo.world/database/malawi",
      "https://boo.world/database/mali",
      "https://boo.world/database/mauritania",
      "https://boo.world/database/mauritius",
      "https://boo.world/database/morocco",
      "https://boo.world/database/mozambique",
      "https://boo.world/database/namibia",
      "https://boo.world/database/niger",
      "https://boo.world/database/nigeria",
      "https://boo.world/database/rwanda",
      "https://boo.world/database/sao-tome-and-principe",
      "https://boo.world/database/senegal",
      "https://boo.world/database/seychelles",
      "https://boo.world/database/sierra-leone",
      "https://boo.world/database/somalia",
      "https://boo.world/database/south-africa",
      "https://boo.world/database/south-sudan",
      "https://boo.world/database/sudan",
      "https://boo.world/database/tanzania",
      "https://boo.world/database/togo",
      "https://boo.world/database/tunisia",
      "https://boo.world/database/uganda",
      "https://boo.world/database/western-sahara",
      "https://boo.world/database/zambia",
      "https://boo.world/database/zimbabwe",
      "https://boo.world/database/afghanistan",
      "https://boo.world/database/armenia",
      "https://boo.world/database/azerbaijan",
      "https://boo.world/database/bahrain",
      "https://boo.world/database/bangladesh",
      "https://boo.world/database/bhutan",
      "https://boo.world/database/brunei",
      "https://boo.world/database/cambodia",
      "https://boo.world/database/china",
      "https://boo.world/database/georgia",
      "https://boo.world/database/hong-kong",
      "https://boo.world/database/india",
      "https://boo.world/database/indonesia",
      "https://boo.world/database/iran",
      "https://boo.world/database/iraq",
      "https://boo.world/database/israel",
      "https://boo.world/database/japan",
      "https://boo.world/database/jordan",
      "https://boo.world/database/kazakhstan",
      "https://boo.world/database/kuwait",
      "https://boo.world/database/kyrgyzstan",
      "https://boo.world/database/laos",
      "https://boo.world/database/lebanon",
      "https://boo.world/database/malaysia",
      "https://boo.world/database/maldives",
      "https://boo.world/database/mongolia",
      "https://boo.world/database/myanmar",
      "https://boo.world/database/nepal",
      "https://boo.world/database/north-korea",
      "https://boo.world/database/oman",
      "https://boo.world/database/pakistan",
      "https://boo.world/database/palestine",
      "https://boo.world/database/philippines",
      "https://boo.world/database/qatar",
      "https://boo.world/database/saudi-arabia",
      "https://boo.world/database/singapore",
      "https://boo.world/database/south-korea",
      "https://boo.world/database/sri-lanka",
      "https://boo.world/database/syria",
      "https://boo.world/database/taiwan",
      "https://boo.world/database/tajikistan",
      "https://boo.world/database/thailand",
      "https://boo.world/database/timor-leste",
      "https://boo.world/database/turkmenistan",
      "https://boo.world/database/united-arab-emirates",
      "https://boo.world/database/uzbekistan",
      "https://boo.world/database/vietnam",
      "https://boo.world/database/yemen",
      "https://boo.world/database/albania",
      "https://boo.world/database/andorra",
      "https://boo.world/database/austria",
      "https://boo.world/database/belarus",
      "https://boo.world/database/belgium",
      "https://boo.world/database/bosnia-and-herzegovina",
      "https://boo.world/database/bulgaria",
      "https://boo.world/database/croatia",
      "https://boo.world/database/cyprus",
      "https://boo.world/database/czechia",
      "https://boo.world/database/denmark",
      "https://boo.world/database/estonia",
      "https://boo.world/database/finland",
      "https://boo.world/database/france",
      "https://boo.world/database/germany",
      "https://boo.world/database/greece",
      "https://boo.world/database/hungary",
      "https://boo.world/database/iceland",
      "https://boo.world/database/ireland",
      "https://boo.world/database/italy",
      "https://boo.world/database/kosovo",
      "https://boo.world/database/latvia",
      "https://boo.world/database/liechtenstein",
      "https://boo.world/database/lithuania",
      "https://boo.world/database/luxembourg",
      "https://boo.world/database/malta",
      "https://boo.world/database/moldova",
      "https://boo.world/database/monaco",
      "https://boo.world/database/montenegro",
      "https://boo.world/database/netherlands",
      "https://boo.world/database/north-macedonia",
      "https://boo.world/database/norway",
      "https://boo.world/database/poland",
      "https://boo.world/database/portugal",
      "https://boo.world/database/romania",
      "https://boo.world/database/russia",
      "https://boo.world/database/san-marino",
      "https://boo.world/database/serbia",
      "https://boo.world/database/slovakia",
      "https://boo.world/database/slovenia",
      "https://boo.world/database/spain",
      "https://boo.world/database/sweden",
      "https://boo.world/database/switzerland",
      "https://boo.world/database/turkey",
      "https://boo.world/database/uk",
      "https://boo.world/database/ukraine",
      "https://boo.world/database/vatican-city",
      "https://boo.world/database/yugoslavia",
      "https://boo.world/database/canada",
      "https://boo.world/database/us",
      "https://boo.world/database/antigua-and-barbuda",
      "https://boo.world/database/bahamas",
      "https://boo.world/database/barbados",
      "https://boo.world/database/belize",
      "https://boo.world/database/bermuda",
      "https://boo.world/database/cayman-islands",
      "https://boo.world/database/costa-rica",
      "https://boo.world/database/cuba",
      "https://boo.world/database/dominican-republic",
      "https://boo.world/database/el-salvador",
      "https://boo.world/database/french-polynesia",
      "https://boo.world/database/grenada",
      "https://boo.world/database/guatemala",
      "https://boo.world/database/haiti",
      "https://boo.world/database/honduras",
      "https://boo.world/database/jamaica",
      "https://boo.world/database/mexico",
      "https://boo.world/database/montserrat",
      "https://boo.world/database/netherlands-antilles",
      "https://boo.world/database/nicaragua",
      "https://boo.world/database/panama",
      "https://boo.world/database/saint-kitts-and-nevis",
      "https://boo.world/database/saint-lucia",
      "https://boo.world/database/saint-vincent-and-the-grenadines",
      "https://boo.world/database/trinidad-and-tobago",
      "https://boo.world/database/australia",
      "https://boo.world/database/fiji",
      "https://boo.world/database/kiribati",
      "https://boo.world/database/marshall-islands",
      "https://boo.world/database/micronesia",
      "https://boo.world/database/nauru",
      "https://boo.world/database/new-zealand",
      "https://boo.world/database/palau",
      "https://boo.world/database/papua-new-guinea",
      "https://boo.world/database/samoa",
      "https://boo.world/database/solomon-islands",
      "https://boo.world/database/tonga",
      "https://boo.world/database/tuvalu",
      "https://boo.world/database/vanuatu",
      "https://boo.world/database/argentina",
      "https://boo.world/database/aruba",
      "https://boo.world/database/bolivia",
      "https://boo.world/database/brazil",
      "https://boo.world/database/chile",
      "https://boo.world/database/colombia",
      "https://boo.world/database/curaçao",
      "https://boo.world/database/ecuador",
      "https://boo.world/database/guyana",
      "https://boo.world/database/paraguay",
      "https://boo.world/database/peru",
      "https://boo.world/database/suriname",
      "https://boo.world/database/uruguay",
      "https://boo.world/database/venezuela",
      "https://boo.world/database/infps",
      "https://boo.world/database/infjs",
      "https://boo.world/database/enfps",
      "https://boo.world/database/enfjs",
      "https://boo.world/database/intps",
      "https://boo.world/database/intjs",
      "https://boo.world/database/entps",
      "https://boo.world/database/entjs",
      "https://boo.world/database/isfps",
      "https://boo.world/database/isfjs",
      "https://boo.world/database/esfps",
      "https://boo.world/database/esfjs",
      "https://boo.world/database/istps",
      "https://boo.world/database/istjs",
      "https://boo.world/database/estps",
      "https://boo.world/database/estjs",
      "https://boo.world/database/1w9s",
      "https://boo.world/database/1w2s",
      "https://boo.world/database/2w1s",
      "https://boo.world/database/2w3s",
      "https://boo.world/database/3w2s",
      "https://boo.world/database/3w4s",
      "https://boo.world/database/4w3s",
      "https://boo.world/database/4w5s",
      "https://boo.world/database/5w4s",
      "https://boo.world/database/5w6s",
      "https://boo.world/database/6w5s",
      "https://boo.world/database/6w7s",
      "https://boo.world/database/7w6s",
      "https://boo.world/database/7w8s",
      "https://boo.world/database/8w7s",
      "https://boo.world/database/8w9s",
      "https://boo.world/database/9w1s",
      "https://boo.world/database/9w8s",
      "https://boo.world/database/type-1-database",
      "https://boo.world/database/type-2-database",
      "https://boo.world/database/type-3-database",
      "https://boo.world/database/type-4-database",
      "https://boo.world/database/type-5-database",
      "https://boo.world/database/type-6-database",
      "https://boo.world/database/type-7-database",
      "https://boo.world/database/type-8-database",
      "https://boo.world/database/type-9-database",
      "https://boo.world/database/aries",
      "https://boo.world/database/tauruses",
      "https://boo.world/database/geminis",
      "https://boo.world/database/cancers",
      "https://boo.world/database/leos",
      "https://boo.world/database/virgos",
      "https://boo.world/database/libras",
      "https://boo.world/database/scorpios",
      "https://boo.world/database/sagittariuses",
      "https://boo.world/database/capricorns",
      "https://boo.world/database/aquariuses",
      "https://boo.world/database/pisces",
      "https://boo.world/database/infp-people",
      "https://boo.world/database/infj-people",
      "https://boo.world/database/enfp-people",
      "https://boo.world/database/enfj-people",
      "https://boo.world/database/intp-people",
      "https://boo.world/database/intj-people",
      "https://boo.world/database/entp-people",
      "https://boo.world/database/entj-people",
      "https://boo.world/database/isfp-people",
      "https://boo.world/database/isfj-people",
      "https://boo.world/database/esfp-people",
      "https://boo.world/database/esfj-people",
      "https://boo.world/database/istp-people",
      "https://boo.world/database/istj-people",
      "https://boo.world/database/estp-people",
      "https://boo.world/database/estj-people",
      "https://boo.world/database/introverted-people",
      "https://boo.world/database/extroverted-people",
      "https://boo.world/database/1w9-people",
      "https://boo.world/database/1w2-people",
      "https://boo.world/database/2w1-people",
      "https://boo.world/database/2w3-people",
      "https://boo.world/database/3w2-people",
      "https://boo.world/database/3w4-people",
      "https://boo.world/database/4w3-people",
      "https://boo.world/database/4w5-people",
      "https://boo.world/database/5w4-people",
      "https://boo.world/database/5w6-people",
      "https://boo.world/database/6w5-people",
      "https://boo.world/database/6w7-people",
      "https://boo.world/database/7w6-people",
      "https://boo.world/database/7w8-people",
      "https://boo.world/database/8w7-people",
      "https://boo.world/database/8w9-people",
      "https://boo.world/database/9w1-people",
      "https://boo.world/database/9w8-people",
      "https://boo.world/database/type-1-people",
      "https://boo.world/database/type-2-people",
      "https://boo.world/database/type-3-people",
      "https://boo.world/database/type-4-people",
      "https://boo.world/database/type-5-people",
      "https://boo.world/database/type-6-people",
      "https://boo.world/database/type-7-people",
      "https://boo.world/database/type-8-people",
      "https://boo.world/database/type-9-people",
      "https://boo.world/database/capricorn-people",
      "https://boo.world/database/aquarius-people",
      "https://boo.world/database/pisces-people",
      "https://boo.world/database/aries-people",
      "https://boo.world/database/taurus-people",
      "https://boo.world/database/gemini-people",
      "https://boo.world/database/cancer-people",
      "https://boo.world/database/leo-people",
      "https://boo.world/database/virgo-people",
      "https://boo.world/database/libra-people",
      "https://boo.world/database/scorpio-people",
      "https://boo.world/database/sagittarius-people",
      "https://boo.world/database/infp-characters",
      "https://boo.world/database/infj-characters",
      "https://boo.world/database/enfp-characters",
      "https://boo.world/database/enfj-characters",
      "https://boo.world/database/intp-characters",
      "https://boo.world/database/intj-characters",
      "https://boo.world/database/entp-characters",
      "https://boo.world/database/entj-characters",
      "https://boo.world/database/isfp-characters",
      "https://boo.world/database/isfj-characters",
      "https://boo.world/database/esfp-characters",
      "https://boo.world/database/esfj-characters",
      "https://boo.world/database/istp-characters",
      "https://boo.world/database/istj-characters",
      "https://boo.world/database/estp-characters",
      "https://boo.world/database/estj-characters",
      "https://boo.world/database/introverted-characters",
      "https://boo.world/database/extroverted-characters",
      "https://boo.world/database/1w9-characters",
      "https://boo.world/database/1w2-characters",
      "https://boo.world/database/2w1-characters",
      "https://boo.world/database/2w3-characters",
      "https://boo.world/database/3w2-characters",
      "https://boo.world/database/3w4-characters",
      "https://boo.world/database/4w3-characters",
      "https://boo.world/database/4w5-characters",
      "https://boo.world/database/5w4-characters",
      "https://boo.world/database/5w6-characters",
      "https://boo.world/database/6w5-characters",
      "https://boo.world/database/6w7-characters",
      "https://boo.world/database/7w6-characters",
      "https://boo.world/database/7w8-characters",
      "https://boo.world/database/8w7-characters",
      "https://boo.world/database/8w9-characters",
      "https://boo.world/database/9w1-characters",
      "https://boo.world/database/9w8-characters",
      "https://boo.world/database/type-1-characters",
      "https://boo.world/database/type-2-characters",
      "https://boo.world/database/type-3-characters",
      "https://boo.world/database/type-4-characters",
      "https://boo.world/database/type-5-characters",
      "https://boo.world/database/type-6-characters",
      "https://boo.world/database/type-7-characters",
      "https://boo.world/database/type-8-characters",
      "https://boo.world/database/type-9-characters",
      "https://boo.world/database/capricorn-characters",
      "https://boo.world/database/aquarius-characters",
      "https://boo.world/database/pisces-characters",
      "https://boo.world/database/aries-characters",
      "https://boo.world/database/taurus-characters",
      "https://boo.world/database/gemini-characters",
      "https://boo.world/database/cancer-characters",
      "https://boo.world/database/leo-characters",
      "https://boo.world/database/virgo-characters",
      "https://boo.world/database/libra-characters",
      "https://boo.world/database/scorpio-characters",
      "https://boo.world/database/sagittarius-characters",
      "https://boo.world/database/african-infps",
      "https://boo.world/database/african-infjs",
      "https://boo.world/database/african-enfps",
      "https://boo.world/database/african-enfjs",
      "https://boo.world/database/african-intps",
      "https://boo.world/database/african-intjs",
      "https://boo.world/database/african-entps",
      "https://boo.world/database/african-entjs",
      "https://boo.world/database/african-isfps",
      "https://boo.world/database/african-isfjs",
      "https://boo.world/database/african-esfps",
      "https://boo.world/database/african-esfjs",
      "https://boo.world/database/african-istps",
      "https://boo.world/database/african-istjs"
    ]);

    expect(categoryUrls.length).to.eql(156);
    expect(categoryUrls).to.eql([
      'https://boo.world/database/anime',
      'https://boo.world/database/anime/infp-anime-characters',
      'https://boo.world/database/anime/infj-anime-characters',
      'https://boo.world/database/anime/enfp-anime-characters',
      'https://boo.world/database/anime/enfj-anime-characters',
      'https://boo.world/database/anime/intp-anime-characters',
      'https://boo.world/database/anime/intj-anime-characters',
      'https://boo.world/database/anime/entp-anime-characters',
      'https://boo.world/database/anime/entj-anime-characters',
      'https://boo.world/database/anime/isfp-anime-characters',
      'https://boo.world/database/anime/isfj-anime-characters',
      'https://boo.world/database/anime/esfp-anime-characters',
      'https://boo.world/database/anime/esfj-anime-characters',
      'https://boo.world/database/anime/istp-anime-characters',
      'https://boo.world/database/anime/istj-anime-characters',
      'https://boo.world/database/anime/estp-anime-characters',
      'https://boo.world/database/anime/estj-anime-characters',
      'https://boo.world/database/anime/introverted-anime-characters',
      'https://boo.world/database/anime/extroverted-anime-characters',
      'https://boo.world/database/anime/1w9-anime-characters',
      'https://boo.world/database/anime/1w2-anime-characters',
      'https://boo.world/database/anime/2w1-anime-characters',
      'https://boo.world/database/anime/2w3-anime-characters',
      'https://boo.world/database/anime/3w2-anime-characters',
      'https://boo.world/database/anime/3w4-anime-characters',
      'https://boo.world/database/anime/4w3-anime-characters',
      'https://boo.world/database/anime/4w5-anime-characters',
      'https://boo.world/database/anime/5w4-anime-characters',
      'https://boo.world/database/anime/5w6-anime-characters',
      'https://boo.world/database/anime/6w5-anime-characters',
      'https://boo.world/database/anime/6w7-anime-characters',
      'https://boo.world/database/anime/7w6-anime-characters',
      'https://boo.world/database/anime/7w8-anime-characters',
      'https://boo.world/database/anime/8w7-anime-characters',
      'https://boo.world/database/anime/8w9-anime-characters',
      'https://boo.world/database/anime/9w1-anime-characters',
      'https://boo.world/database/anime/9w8-anime-characters',
      'https://boo.world/database/anime/type-1-anime-characters',
      'https://boo.world/database/anime/type-2-anime-characters',
      'https://boo.world/database/anime/type-3-anime-characters',
      'https://boo.world/database/anime/type-4-anime-characters',
      'https://boo.world/database/anime/type-5-anime-characters',
      'https://boo.world/database/anime/type-6-anime-characters',
      'https://boo.world/database/anime/type-7-anime-characters',
      'https://boo.world/database/anime/type-8-anime-characters',
      'https://boo.world/database/anime/type-9-anime-characters',
      'https://boo.world/database/anime/capricorn-anime-characters',
      'https://boo.world/database/anime/aquarius-anime-characters',
      'https://boo.world/database/anime/pisces-anime-characters',
      'https://boo.world/database/anime/aries-anime-characters',
      'https://boo.world/database/anime/taurus-anime-characters',
      'https://boo.world/database/anime/gemini-anime-characters',
      'https://boo.world/database/anime/cancer-anime-characters',
      'https://boo.world/database/anime/leo-anime-characters',
      'https://boo.world/database/anime/virgo-anime-characters',
      'https://boo.world/database/anime/libra-anime-characters',
      'https://boo.world/database/anime/scorpio-anime-characters',
      'https://boo.world/database/anime/sagittarius-anime-characters',
      'https://boo.world/database/anime/asian-anime-characters',
      'https://boo.world/database/anime/asian-estj-anime-characters',
      'https://boo.world/database/anime/asian-1w9-anime-characters',
      'https://boo.world/database/anime/asian-aries-anime-characters',
      'https://boo.world/database/anime/asian-extroverted-anime-characters',
      'https://boo.world/database/anime/asian-type-1-anime-characters',
      'https://boo.world/database/anime/north-american-anime-characters',
      'https://boo.world/database/anime/north-american-estj-anime-characters',
      'https://boo.world/database/anime/north-american-1w9-anime-characters',
      'https://boo.world/database/anime/north-american-aries-anime-characters',
      'https://boo.world/database/anime/north-american-extroverted-anime-characters',
      'https://boo.world/database/anime/north-american-type-1-anime-characters',
      'https://boo.world/database/anime/indian-anime-characters',
      'https://boo.world/database/anime/indian-estj-anime-characters',
      'https://boo.world/database/anime/indian-1w9-anime-characters',
      'https://boo.world/database/anime/indian-aries-anime-characters',
      'https://boo.world/database/anime/indian-extroverted-anime-characters',
      'https://boo.world/database/anime/indian-type-1-anime-characters',
      'https://boo.world/database/anime/nepali-anime-characters',
      'https://boo.world/database/anime/nepali-estj-anime-characters',
      'https://boo.world/database/anime/nepali-1w9-anime-characters',
      'https://boo.world/database/anime/nepali-aries-anime-characters',
      'https://boo.world/database/anime/nepali-extroverted-anime-characters',
      'https://boo.world/database/anime/nepali-type-1-anime-characters',
      'https://boo.world/database/anime/haitian-anime-characters',
      'https://boo.world/database/anime/haitian-estj-anime-characters',
      'https://boo.world/database/anime/haitian-1w9-anime-characters',
      'https://boo.world/database/anime/haitian-aries-anime-characters',
      'https://boo.world/database/anime/haitian-extroverted-anime-characters',
      'https://boo.world/database/anime/haitian-type-1-anime-characters',
      'https://boo.world/database/tv',
      'https://boo.world/database/tv/infp-tv-show-characters',
      'https://boo.world/database/tv/infj-tv-show-characters',
      'https://boo.world/database/tv/enfp-tv-show-characters',
      'https://boo.world/database/tv/enfj-tv-show-characters',
      'https://boo.world/database/tv/intp-tv-show-characters',
      'https://boo.world/database/tv/intj-tv-show-characters',
      'https://boo.world/database/tv/entp-tv-show-characters',
      'https://boo.world/database/tv/entj-tv-show-characters',
      'https://boo.world/database/tv/isfp-tv-show-characters',
      'https://boo.world/database/tv/isfj-tv-show-characters',
      'https://boo.world/database/tv/esfp-tv-show-characters',
      'https://boo.world/database/tv/esfj-tv-show-characters',
      'https://boo.world/database/tv/istp-tv-show-characters',
      'https://boo.world/database/tv/istj-tv-show-characters',
      'https://boo.world/database/tv/estp-tv-show-characters',
      'https://boo.world/database/tv/estj-tv-show-characters',
      'https://boo.world/database/tv/introverted-tv-show-characters',
      'https://boo.world/database/tv/extroverted-tv-show-characters',
      'https://boo.world/database/tv/1w9-tv-show-characters',
      'https://boo.world/database/tv/1w2-tv-show-characters',
      'https://boo.world/database/tv/2w1-tv-show-characters',
      'https://boo.world/database/tv/2w3-tv-show-characters',
      'https://boo.world/database/tv/3w2-tv-show-characters',
      'https://boo.world/database/tv/3w4-tv-show-characters',
      'https://boo.world/database/tv/4w3-tv-show-characters',
      'https://boo.world/database/tv/4w5-tv-show-characters',
      'https://boo.world/database/tv/5w4-tv-show-characters',
      'https://boo.world/database/tv/5w6-tv-show-characters',
      'https://boo.world/database/tv/6w5-tv-show-characters',
      'https://boo.world/database/tv/6w7-tv-show-characters',
      'https://boo.world/database/tv/7w6-tv-show-characters',
      'https://boo.world/database/tv/7w8-tv-show-characters',
      'https://boo.world/database/tv/8w7-tv-show-characters',
      'https://boo.world/database/tv/8w9-tv-show-characters',
      'https://boo.world/database/tv/9w1-tv-show-characters',
      'https://boo.world/database/tv/9w8-tv-show-characters',
      'https://boo.world/database/tv/type-1-tv-show-characters',
      'https://boo.world/database/tv/type-2-tv-show-characters',
      'https://boo.world/database/tv/type-3-tv-show-characters',
      'https://boo.world/database/tv/type-4-tv-show-characters',
      'https://boo.world/database/tv/type-5-tv-show-characters',
      'https://boo.world/database/tv/type-6-tv-show-characters',
      'https://boo.world/database/tv/type-7-tv-show-characters',
      'https://boo.world/database/tv/type-8-tv-show-characters',
      'https://boo.world/database/tv/type-9-tv-show-characters',
      'https://boo.world/database/tv/capricorn-tv-show-characters',
      'https://boo.world/database/tv/aquarius-tv-show-characters',
      'https://boo.world/database/tv/pisces-tv-show-characters',
      'https://boo.world/database/tv/aries-tv-show-characters',
      'https://boo.world/database/tv/taurus-tv-show-characters',
      'https://boo.world/database/tv/gemini-tv-show-characters',
      'https://boo.world/database/tv/cancer-tv-show-characters',
      'https://boo.world/database/tv/leo-tv-show-characters',
      'https://boo.world/database/tv/virgo-tv-show-characters',
      'https://boo.world/database/tv/libra-tv-show-characters',
      'https://boo.world/database/tv/scorpio-tv-show-characters',
      'https://boo.world/database/tv/sagittarius-tv-show-characters',
      'https://boo.world/database/tv/african-tv-show-characters',
      'https://boo.world/database/tv/african-entp-tv-show-characters',
      'https://boo.world/database/tv/african-1w2-tv-show-characters',
      'https://boo.world/database/tv/african-extroverted-tv-show-characters',
      'https://boo.world/database/tv/african-type-1-tv-show-characters',
      'https://boo.world/database/tv/chadian-tv-show-characters',
      'https://boo.world/database/tv/chadian-entp-tv-show-characters',
      'https://boo.world/database/tv/chadian-1w2-tv-show-characters',
      'https://boo.world/database/tv/chadian-extroverted-tv-show-characters',
      'https://boo.world/database/tv/chadian-type-1-tv-show-characters'
    ])

    expect(subcategoryUrls).to.eql([
      'https://boo.world/database/anime/pokemon-personality-types',
      'https://boo.world/database/anime/bleach-personality-types',
      'https://boo.world/database/anime/naruto-personality-types',
      'https://boo.world/database/tv/manifest-personality-types'
    ]);

    let LastModifiedDate = new Date('2024-12-12')

    expect(profileUrls).to.eql({
      en:[
      {url: 'https://boo.world/database/profile/1/naruto-personality-type', lastModified: LastModifiedDate },
      {url: 'https://boo.world/database/profile/2/sasuke-uchiha-personality-type', lastModified: LastModifiedDate },
      {url: 'https://boo.world/database/profile/3/sakura-personality-type', lastModified: LastModifiedDate, image: 'sakura.jpg'},
      {url: 'https://boo.world/database/profile/4/bleach-personality-type', lastModified: LastModifiedDate },
      {url: 'https://boo.world/database/profile/5/pikachu-personality-type', lastModified: LastModifiedDate, image: 'pikachu.jpg'},
      {url: 'https://boo.world/database/profile/6/manifest-personality-type', lastModified: LastModifiedDate },
    ],
    fr:[
      {url: 'https://boo.world/database/profile/1/naruto-personality-type', lastModified: LastModifiedDate },
    ]
  });

    res = await request(app)
      .post('/v1/worker/updateSitemap')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
  });

  it('profile sort', async () => {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);

    profile = await Profile.findOne({name: 'Naruto'});
    res = await request(app)
      .post('/v1/comment')
      .set('authorization', 0)
      .send({
        parentId: profile._id,
        vote: { mbti: 'INTJ' },
      });

    profile = await Profile.findOne({name: 'Sakura'});
    res = await request(app)
      .post('/v1/comment')
      .set('authorization', 0)
      .send({
        parentId: profile._id,
        vote: { mbti: 'INTJ' },
      });
    expect(res.status).to.equal(200);
    profile = await Profile.findOne({name: 'Sakura'});
    expect(profile.sort).to.equal(101);
    profile = await Profile.findOne({name: 'Naruto'});
    expect(profile.sort).to.equal(1);
    profile = await Profile.findOne({name: 'Sasuke Uchiha'});
    expect(profile.sort).to.equal(0);

    res = await request(app)
      .get('/web/database/profiles')
      .query({ subcategoryId: 1 });
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(2);
    expect(res.body.profiles[0].name).to.equal('Sakura');
    expect(res.body.profiles[1].name).to.equal('Naruto');
  });

  it('profile counts', async () => {
    res = await request(app)
      .post('/v1/worker/backFillPersonalityData')
    expect(res.status).to.equal(200);

    doc = await Category.findOne({id: 3});
    expect(doc.numSubcategories).to.equal(3);
    expect(doc.numProfiles).to.equal(5);
    expect(JSON.parse(JSON.stringify(doc.personalityCount))).to.eql({
      mbti: { ENTJ: 1, ESTJ: 2, ENTP: 2 },
      enneagram: { '1w2': 2, '1w9': 2 },
      horoscope: { Taurus: 2, Aries: 2 }
    });

    doc = await Category.findOne({id: 4});
    expect(doc.numSubcategories).to.equal(1);
    expect(doc.numProfiles).to.equal(1);
    expect(JSON.parse(JSON.stringify(doc.personalityCount))).to.eql({
      mbti: { ENTP: 1 },
      enneagram: { '1w2': 1 },
      horoscope: { }
    });

    doc = await Subcategory.findOne({id: 1});
    expect(doc.numProfiles).to.equal(3);
    expect(JSON.parse(JSON.stringify(doc.personalityCount))).to.eql({
      mbti: { ESTJ: 2, ENTP: 1 },
      enneagram: { '1w2': 1, '1w9': 1 },
      horoscope: { Taurus: 1, Aries: 1 }
    });

    profile = await Profile.findOne({ id: 1 });

    for (let i = 100; i < 106; i++) {
      res = await request(app)
        .put('/v1/user/initApp')
        .set('authorization', i);
      expect(res.status).to.equal(200);

      res = await request(app)
        .post('/v1/comment')
        .set('authorization', i)
        .send({
          parentId: profile._id,
          vote: { mbti: 'INFJ', horoscope: 'Pisces', enneagram: '8w9' },
        });
      expect(res.status).to.equal(200);
    }

    doc = await Category.findOne({ id: 3 });
    expect(doc.numSubcategories).to.equal(3);
    expect(doc.numProfiles).to.equal(5);
    expect(JSON.parse(JSON.stringify(doc.personalityCount))).to.eql({
      mbti: { ESTJ: 1, ENTP: 2, ENTJ: 1, INFJ: 1 },
      enneagram: { '1w2': 2, '1w9': 1, '8w9': 1 },
      horoscope: { Aries: 1, Taurus: 2, Pisces: 1 }
    });

    doc = await Subcategory.findOne({ id: 1 });

    expect(doc.numProfiles).to.equal(3);
    expect(JSON.parse(JSON.stringify(doc.personalityCount))).to.eql({
      mbti: { ESTJ: 1, ENTP: 1, INFJ: 1 },
      enneagram: { '1w9': 0, '1w2': 1, '8w9': 1 },
      horoscope: { Taurus: 1, Aries: 0, Pisces: 1 }
    });

  });

  it('subcategories', async () => {
    // basic case - check response fields
    res = await request(app)
      .get('/web/database/subcategoriesWithProfiles')
      .query({ categoryId: 3 });
    expect(res.status).to.equal(200);
    console.log(JSON.stringify(res.body, null, 2));
    expect(res.body.subcategoriesWithProfiles.length).to.equal(2);
    expect(res.body.subcategoriesWithProfiles[0].slug).to.equal('pokemon');
    expect(res.body.subcategoriesWithProfiles[0].url).to.equal('/database/anime/pokemon');
    expect(res.body.subcategoriesWithProfiles[0].profiles.length).to.equal(1);
    expect(res.body.subcategoriesWithProfiles[0].profiles[0].name).to.equal('Pikachu');
    expect(res.body.subcategoriesWithProfiles[0].profiles[0].url).to.equal('/database/profile/5/pikachu-personality-type');
    expect(res.body.subcategoriesWithProfiles[0].profiles[0].image).to.equal('MOCK_IMAGE_DOMAIN/pikachu.jpg');
    expect(res.body.subcategoriesWithProfiles[0].profiles[0].subcategories.length).to.equal(1);
    expect(res.body.subcategoriesWithProfiles[0].profiles[0].subcategories[0].slug).to.equal('pokemon');
    expect(res.body.subcategoriesWithProfiles[0].profiles[0].subcategories[0].url).to.equal('/database/anime/pokemon');
    expect(res.body.subcategoriesWithProfiles[0].profiles[0].categories.length).to.equal(1);
    expect(res.body.subcategoriesWithProfiles[0].profiles[0].categories[0].slug).to.equal('anime');
    expect(res.body.subcategoriesWithProfiles[0].profiles[0].categories[0].url).to.equal('/database/anime');
    expect(res.body.subcategoriesWithProfiles[1].slug).to.equal('bleach');
    expect(res.body.subcategoriesWithProfiles[1].profiles.length).to.equal(1);
    expect(res.body.subcategoriesWithProfiles[1].profiles[0].name).to.equal('Bleach');
    expect(res.body.subcategoriesWithProfiles[1].profiles[0].image).to.equal();

    // test paging
    res = await request(app)
      .get('/web/database/subcategoriesWithProfiles')
      .query({ categoryId: 3, page: 2 });
    expect(res.status).to.equal(200);
    expect(res.body.subcategoriesWithProfiles.length).to.equal(1);
    expect(res.body.subcategoriesWithProfiles[0].slug).to.equal('naruto');
    expect(res.body.subcategoriesWithProfiles[0].profiles.length).to.equal(3);

    res = await request(app)
      .get('/web/database/subcategoriesWithProfiles')
      .query({ categoryId: 3, page: 3 });
    expect(res.status).to.equal(200);
    expect(res.body.subcategoriesWithProfiles.length).to.equal(0);

    // test mbti filter
    res = await request(app)
      .get('/web/database/subcategoriesWithProfiles')
      .query({ categoryId: 3, filter: 'entp' });
    expect(res.status).to.equal(200);
    expect(res.body.subcategoriesWithProfiles.length).to.equal(2);
    expect(res.body.subcategoriesWithProfiles[0].slug).to.equal('pokemon');
    expect(res.body.subcategoriesWithProfiles[0].profiles.length).to.equal(1);
    expect(res.body.subcategoriesWithProfiles[1].slug).to.equal('bleach');
    expect(res.body.subcategoriesWithProfiles[1].profiles.length).to.equal(0);

    res = await request(app)
      .get('/web/database/subcategoriesWithProfiles')
      .query({ categoryId: 3, filter: 'entp', page: 2 });
    expect(res.status).to.equal(200);
    expect(res.body.subcategoriesWithProfiles.length).to.equal(1);
    expect(res.body.subcategoriesWithProfiles[0].slug).to.equal('naruto');
    expect(res.body.subcategoriesWithProfiles[0].profiles.length).to.equal(1);

    // test enneagram filter
    res = await request(app)
      .get('/web/database/subcategoriesWithProfiles')
      .query({ categoryId: 3, filter: '1w9' });
    expect(res.status).to.equal(200);
    expect(res.body.subcategoriesWithProfiles.length).to.equal(2);
    expect(res.body.subcategoriesWithProfiles[0].slug).to.equal('pokemon');
    expect(res.body.subcategoriesWithProfiles[0].profiles.length).to.equal(0);
    expect(res.body.subcategoriesWithProfiles[1].slug).to.equal('bleach');
    expect(res.body.subcategoriesWithProfiles[1].profiles.length).to.equal(1);

    // test horoscope filter
    res = await request(app)
      .get('/web/database/subcategoriesWithProfiles')
      .query({ categoryId: 3, filter: 'taurus' });
    expect(res.status).to.equal(200);
    expect(res.body.subcategoriesWithProfiles.length).to.equal(2);
    expect(res.body.subcategoriesWithProfiles[0].slug).to.equal('pokemon');
    expect(res.body.subcategoriesWithProfiles[0].profiles.length).to.equal(1);
    expect(res.body.subcategoriesWithProfiles[1].slug).to.equal('bleach');
    expect(res.body.subcategoriesWithProfiles[1].profiles.length).to.equal(0);
  });

  it('error handling for database routes', async () => {
    res = await request(app)
      .get('/web/database/profile')
      .query({ id: 'invalid' });
    expect(res.status).to.equal(422);

    res = await request(app)
      .get('/web/database/profiles')
      .query({ subcategoryId: 'invalid' });
    expect(res.status).to.equal(422);

    res = await request(app)
      .get('/web/database/subcategoriesWithProfiles')
      .query({ categoryId: 'invalid' });
    expect(res.status).to.equal(422);
  });

  it('PersonalityDatabaseChangeTracker detects new profiles and subcategories', async () => {

    let changes = await personalityDatabaseChangeHelper.checkPersonalityDatabaseChanges();
    expect(changes.hasAnyChanges).to.equal(true);
    expect(changes.categories).to.equal(true);
    expect(changes.subcategories).to.equal(true);
    expect(changes.profiles).to.equal(true);


    changes = await personalityDatabaseChangeHelper.checkPersonalityDatabaseChanges();
    expect(changes.hasAnyChanges).to.equal(false);
    expect(changes.categories).to.equal(false);
    expect(changes.subcategories).to.equal(false);
    expect(changes.profiles).to.equal(false);

    // Add a new subcategory
    const newSubcategory = new Subcategory({
      id: 9999,
      sort: 1,
      name: 'Test Subcategory',
      slug: 'test-subcategory',
      category: 3
    });
    await newSubcategory.save();

    // Add a new profile
    const newProfile = new Profile({
      id: 9999,
      sort: 1,
      name: 'Test Profile',
      slug: 'test-profile',
      subcategories: [9999],
      mbti: 'INFP',
      enneagram: '4w5',
      horoscope: 'Pisces'
    });
    await newProfile.save();

    // Check for changes - should detect both new subcategory and profile
    changes = await personalityDatabaseChangeHelper.checkPersonalityDatabaseChanges();
    expect(changes.hasAnyChanges).to.equal(true);
    expect(changes.subcategories).to.equal(true);
    expect(changes.profiles).to.equal(true);
    expect(changes.categories).to.equal(false);

    // Update tracker to reflect new state
    await personalityDatabaseChangeHelper.checkPersonalityDatabaseChanges();

    // Verify no changes after update
    changes = await personalityDatabaseChangeHelper.checkPersonalityDatabaseChanges();
    expect(changes.hasAnyChanges).to.equal(false);
  });

  it('get profile intro', async () => {
    profile = await Profile.findOne({id: 6});
    profile.intros = {
      en: {
        intro: 'english',
        mbti: 'mbti',
        enneagram: "NULL"
      },
      es: {
        intro: 'spanish',
        mbti: 'NULL',
        enneagram: 'NULL'
      }
    };
    profile.translatedLanguages = ['en','es'];
    await profile.save();

    res = await request(app)
      .get('/web/database/profile')
      .query({ id: 6 });
    expect(res.status).to.equal(200);
    console.log(res.body.profile);
    expect(res.body.profile.intros).to.eql({ en: { intro: 'english', mbti: 'mbti', enneagram: 'Manifest is an Enneagram One personality type with a Two wing or 1w2. Enneagram 1w2s tend to be extroverted and outgoing partnered with a warm nature. They are empathetic and understanding and may feel inclined to help people around them. Being innately excellent problem-solvers, they may become a bit too critical and controlling to deal with situations their way.' } });
    expect(res.body.profile.translatedLanguages).to.eql(['en','es']);

    res = await request(app)
      .get('/web/database/profile')
      .query({ id: 6, locale: 'de' });
    expect(res.status).to.equal(200);
    console.log(res.body.profile);
    expect(res.body.profile.intros).to.eql({ en: { intro: 'english', mbti: 'mbti', enneagram: 'Manifest is an Enneagram One personality type with a Two wing or 1w2. Enneagram 1w2s tend to be extroverted and outgoing partnered with a warm nature. They are empathetic and understanding and may feel inclined to help people around them. Being innately excellent problem-solvers, they may become a bit too critical and controlling to deal with situations their way.' } });
    expect(res.body.profile.translatedLanguages).to.eql(['en','es']);

    res = await request(app)
      .get('/web/database/profile')
      .query({ id: 6, locale: 'es' });
    expect(res.status).to.equal(200);
    console.log(res.body.profile);
    expect(res.body.profile.intros).to.eql({ en: { intro: 'english', mbti: 'mbti', enneagram: 'NULL' }, es: { intro: 'spanish', mbti: 'Los ENTP, como individuos, tienden a ser descritos frecuentemente como \"visionarios\". Son capaces de ver el potencial en las personas y situaciones. Son buenos para leer a los demás y comprender sus pensamientos. Son arriesgados, les encanta la vida y no rechazarán oportunidades de diversión y aventura.\n\nLos ENTP siempre están buscando nuevas ideas y no tienen miedo de experimentar. También son de mente abierta y tolerantes, y respetan los puntos de vista de otras personas. Les gustan los amigos que son sinceros acerca de sus emociones y creencias. No se toman los desacuerdos de manera personal. Difieren ligeramente en cómo evaluar la compatibilidad. No importa si están en el mismo bando, siempre que vean a otros mantenerse firmes. A pesar de su apariencia aterradora, saben cómo disfrutar y relajarse. Una botella de vino mientras se discuten temas políticos y otros asuntos relevantes captará su atención.', enneagram: 'Manifest es un tipo de personalidad del Enneagrama Uno con una ala Dos o 1w2. Los eneatipos 1w2 tienden a ser extrovertidos y sociables, además de tener una naturaleza cálida. Son empáticos y comprensivos y pueden sentirse inclinados a ayudar a las personas que los rodean. Siendo excelentes solucionadores de problemas de manera innata, pueden llegar a ser un poco críticos y controladores al tratar de manejar las situaciones a su manera.' } });
    expect(res.body.profile.translatedLanguages).to.eql(['en','es']);
  });

  it('profiles', async () => {
    // basic case - check response fields
    res = await request(app)
      .get('/web/database/profiles')
      .query({ subcategoryId: 1 });
    expect(res.status).to.equal(200);
    console.log(JSON.stringify(res.body, null, 2));
    expect(res.body.profiles.length).to.equal(2);
    expect(res.body.profiles[0].name).to.equal('Sakura');
    expect(res.body.profiles[0].url).to.equal('/database/profile/3/sakura-personality-type');
    expect(res.body.profiles[0].subcategories.length).to.equal(1);
    expect(res.body.profiles[0].subcategories[0].slug).to.equal('naruto');
    expect(res.body.profiles[0].subcategories[0].url).to.equal('/database/anime/naruto');
    expect(res.body.profiles[0].categories.length).to.equal(1);
    expect(res.body.profiles[0].categories[0].slug).to.equal('anime');
    expect(res.body.profiles[0].categories[0].url).to.equal('/database/anime');
    expect(res.body.profiles[1].name).to.equal('Naruto');

    // test paging
    res = await request(app)
      .get('/web/database/profiles')
      .query({ subcategoryId: 1, page: 2 });
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(1);
    expect(res.body.profiles[0].name).to.equal('Sasuke Uchiha');

    res = await request(app)
      .get('/web/database/profiles')
      .query({ subcategoryId: 1, page: 3 });
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(0);

    // test mbti filter
    res = await request(app)
      .get('/web/database/profiles')
      .query({ subcategoryId: 1, filter: 'estj' });
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(2);
    expect(res.body.profiles[0].name).to.equal('Sakura');
    expect(res.body.profiles[1].name).to.equal('Naruto');

    res = await request(app)
      .get('/web/database/profiles')
      .query({ subcategoryId: 1, filter: 'estj', page: 2 });
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(0);

    // test enneagram filter
    res = await request(app)
      .get('/web/database/profiles')
      .query({ subcategoryId: 1, filter: '1w2' });
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(1);
    expect(res.body.profiles[0].name).to.equal('Sasuke Uchiha');

    // test horoscope filter
    res = await request(app)
      .get('/web/database/profiles')
      .query({ subcategoryId: 1, filter: 'aries' });
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(1);
    expect(res.body.profiles[0].name).to.equal('Naruto');
  });

  it('get all database profiles', async () => {
    res = await request(app)
      .get('/web/cached/allDatabaseProfiles')
    expect(res.status).to.equal(200);
    expect(res.body.allDatabaseProfiles.length).to.equal(6);
    expect(res.body.allDatabaseProfiles).to.deep.include({
      id: 1,
      name: 'Naruto',
      subcategories: [1],
      vote: {
        totalCount: 0,
      },
    });
  });

  it('linked keywords', async () => {
    await Profile.insertMany([
      {
        id: 100, sort: 5, name: 'Wayne Rooney', mbti: 'INFJ', subcategories: [3], enneagram: '1w2', horoscope: 'Taurus', image: 'pikachu.jpg', countries: ['England'], intros: { en: { intro: 'Wayne Rooney played with zidane watched pokemon in tv'}}
      },
      {
        id: 101, sort: 5, name: 'Zidane', mbti: 'INFJ', subcategories: [3], enneagram: '1w2', horoscope: 'Taurus', image: 'pikachu.jpg', countries: ['France']
      },
      {
        id: 102, sort: 5, name: 'A.C. Peterson', mbti: 'INFJ', subcategories: [3], enneagram: '1w2', horoscope: 'Taurus', image: 'pikachu.jpg', countries: ['France']
      },
      {
        id: 103, sort: 5, name: 'Gu Jae-sik', mbti: 'INFJ', subcategories: [3], enneagram: '1w2', horoscope: 'Taurus', image: 'pikachu.jpg', countries: ['France']
      },
    ])
    await databaseLib.backfillLinkedDatabaseKeywords();

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);

    res = await request(app)
      .post('/v1/question')
      .set('authorization', 0)
      .send({
        interestName: 'kpop',
        title: 'Naruto is on tv now',
        text: '1 SASUKE UCHIHA 2',
      });
    expect(res.status).to.equal(200);

    await new Promise((resolve) => setTimeout(resolve, 100))

    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(1);
    expect(res.body.questions[0].linkedCategories).to.eql([{ id: 4, slug: 'tv' }]);
    expect(res.body.questions[0].linkedSubcategories).to.eql([{ id: 1, slug: 'naruto', categoryId: 3 }]);
    expect(res.body.questions[0].linkedProfiles).to.eql([
      { id: 1, slug: 'naruto' },
      { id: 2, slug: 'sasuke-uchiha' },
    ]);
    const q1Id = res.body.questions[0]._id;

    res = await request(app)
      .post('/v1/comment')
      .set('authorization', 0)
      .send({
        questionId: q1Id,
        parentId: q1Id,
        text: 'pokemon and anime and pikachu',
      });
    expect(res.status).to.equal(200);

    await new Promise((resolve) => setTimeout(resolve, 100))

    res = await request(app)
      .get('/v1/comment')
      .query({ parentId: q1Id })
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.comments.length).to.equal(1);
    expect(res.body.comments[0].linkedCategories).to.eql([{ id: 3, slug: 'anime' }]);
    expect(res.body.comments[0].linkedSubcategories).to.eql([{ id: 3, slug: 'pokemon', categoryId: 3 }]);
    expect(res.body.comments[0].linkedProfiles).to.eql([{ id: 5, slug: 'pikachu' }]);

    // test backfill
    await Question.updateMany({}, { $set: { linkedCategories: [], linkedSubcategories: [], linkedProfiles: [] } });
    await Comment.updateMany({}, { $set: { linkedCategories: [], linkedSubcategories: [], linkedProfiles: [] } });

    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(1);
    expect(res.body.questions[0].linkedCategories).to.eql([]);
    expect(res.body.questions[0].linkedSubcategories).to.eql([]);
    expect(res.body.questions[0].linkedProfiles).to.eql([]);

    res = await request(app)
      .get('/v1/comment')
      .query({ parentId: q1Id })
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.comments.length).to.equal(1);
    expect(res.body.comments[0].linkedCategories).to.eql([]);
    expect(res.body.comments[0].linkedSubcategories).to.eql([]);
    expect(res.body.comments[0].linkedProfiles).to.eql([]);

    await databaseLib.backfillLinkedDatabaseKeywords();

    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(1);
    expect(res.body.questions[0].linkedCategories).to.eql([{ id: 4, slug: 'tv' }]);
    expect(res.body.questions[0].linkedSubcategories).to.eql([{ id: 1, slug: 'naruto', categoryId: 3 }]);
    expect(res.body.questions[0].linkedProfiles).to.eql([
      { id: 1, slug: 'naruto' },
      { id: 2, slug: 'sasuke-uchiha' },
    ]);

    res = await request(app)
      .get('/v1/comment')
      .query({ parentId: q1Id })
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.comments.length).to.equal(1);
    expect(res.body.comments[0].linkedCategories).to.eql([{ id: 3, slug: 'anime' }]);
    expect(res.body.comments[0].linkedSubcategories).to.eql([{ id: 3, slug: 'pokemon', categoryId: 3 }]);
    expect(res.body.comments[0].linkedProfiles).to.eql([{ id: 5, slug: 'pikachu' }]);

    res = await request(app)
    .get('/web/database/profile')
    .query({ id: 100, locale: 'en' });
    expect(res.status).to.equal(200);
    expect(res.body.profile.linkedCategories).to.eql([{ id: 4, slug: 'tv' }]);
    expect(res.body.profile.linkedSubcategories).to.eql([{ id: 3, slug: 'pokemon', categoryId: 3 }]);
    expect(res.body.profile.linkedProfiles).to.eql([{ id: 101, slug: 'zidane' }]);

    res = await request(app)
    .get('/web/database/profile')
    .query({ id: 101, locale: 'en' });
    expect(res.status).to.equal(200);
    expect(res.body.profile.linkedCategories).to.eql([]);
    expect(res.body.profile.linkedSubcategories).to.eql([]);
    expect(res.body.profile.linkedProfiles).to.eql([]);

    await new Promise((resolve) => setTimeout(resolve, 50))

    profile = await Profile.findOne({ id: 101 })
    profile.mbti = 'INFJ'
    profile.linkedKeywordsUpdatedAt = undefined
    profile.linkedKeywordsProcessingStartAt = undefined
    profile.intros = {
      en: {
        intro: 'Wayne Rooney played with zidane, watched in tv and pokemon in it A.C. Peterson and, Gu Jae-sik hero'
      }
    }
    await profile.save()

    for (let i = 5; i < 11; i++) {
      res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', i)
      expect(res.status).to.equal(200);
      let mbti = 'INTJ'
      if (i == 9) mbti = 'INFJ'
      res = await request(app)
      .post('/v1/comment')
      .set('authorization', i)
      .send({
        parentId: profile._id,
        vote: { mbti: mbti },
      });
      expect(res.status).to.equal(200);
    }

    res = await request(app)
    .get('/web/database/profile')
    .query({ id: 101, locale: 'en' });
    expect(res.status).to.equal(200);

    await new Promise((resolve) => setTimeout(resolve, 200))

    res = await request(app)
    .get('/web/database/profile')
    .query({ id: 101, locale: 'en' });
    expect(res.status).to.equal(200);
    expect(res.body.profile.linkedCategories).to.eql([{ id: 4, slug: 'tv' }]);
    expect(res.body.profile.linkedSubcategories).to.eql([{ id: 3, slug: 'pokemon', categoryId: 3 }]);
    expect(res.body.profile.linkedProfiles).to.eql([
      { id: 100, slug: 'wayne-rooney' },
      { id: 102, slug: 'ac-peterson' },
      { id: 103, slug: 'gu-jae-sik' },
    ]);
  });

  it('imageAttribution', async () => {
    await Profile.insertMany([
      {
        id: 100, sort: 5, name: 'Wayne Rooney', mbti: 'INFJ', subcategories: [3], enneagram: '1w2', horoscope: 'Taurus', image: 'waynerooneyimage.jpg', imageAttribution: 'waynerooney.jpg',
      },
      {
        id: 23, sort: 5, name: 'Mickel Jordan', mbti: 'INFJ', subcategories: [3], enneagram: '1w2', horoscope: 'Taurus', image: 'micheljordanimage.jpg',
      }
    ])

    res = await request(app)
    .get('/web/database/profile')
    .query({ id: 100 });
    expect(res.status).to.equal(200);
    expect(res.body.profile.imageAttribution).to.eql('waynerooney.jpg');

    res = await request(app)
    .get('/web/database/profile')
    .query({ id: 23 });
    expect(res.status).to.equal(200);
    expect(res.body.profile.imageAttribution).to.eql(undefined);

  });
});

describe('updateSitemap', () => {
  let listObjectsStub;
  let deleteObjectsStub;

  before(() => {
    createStubs();
  });

  beforeEach(() => {
    // Create new stubs for each test
    listObjectsStub = sinon.stub(s3, 'listObjectsV2');
    deleteObjectsStub = sinon.stub(s3, 'deleteObjects');
  });

  afterEach(() => {
    // Restore the stubs after each test
    sinon.restore();
  });

  it('should correctly update sitemap', async () => {
    listObjectsStub.onFirstCall().returns({
      promise: () => Promise.resolve({
        'IsTruncated': true,
        'Contents': [
          {
            'Key': 'sitemaps/sitemap_meme_kit_0.xml',
            'LastModified': '2023-07-08T03:05:14.993Z',
            'Size': 1234,
            'StorageClass': 'STANDARD'
          },
          {
            'Key': 'sitemaps/invalid1.xml',
            'LastModified': '2023-07-08T03:05:14.993Z',
            'Size': 5678,
            'StorageClass': 'STANDARD'
          }
        ],
        'Name': 'MOCK_SOULVERSE_S3_BUCKET',
        'Prefix': 'sitemaps/',
        'MaxKeys': 1000,
        'CommonPrefixes': [],
        'KeyCount': 2,
        'NextContinuationToken': 'token1'
      })
    });
    listObjectsStub.onSecondCall().returns({
      promise: () => Promise.resolve({
        'IsTruncated': false,
        'Contents': [
          {
            'Key': 'sitemaps/sitemap_assertive_turbulent_resources.xml',
            'LastModified': '2023-07-08T03:05:14.993Z',
            'Size': 1234,
            'StorageClass': 'STANDARD'
          },
          {
            'Key': 'sitemaps/invalid2.xml',
            'LastModified': '2023-07-08T03:05:14.993Z',
            'Size': 5678,
            'StorageClass': 'STANDARD'
          }
        ],
        'Name': 'MOCK_SOULVERSE_S3_BUCKET',
        'Prefix': 'sitemaps/',
        'MaxKeys': 1000,
        'CommonPrefixes': [],
        'KeyCount': 2,
      })
    });

    deleteObjectsStub.returns({
      promise: () => Promise.resolve({})
    });

    const res = await request(app)
      .post('/v1/worker/updateSitemap')
      .set('authorization', '0');
    expect(res.status).to.equal(200);

    // Debug: Log the number of times deleteObjects was called
    expect(listObjectsStub.callCount).to.equal(2);
    expect(listObjectsStub.calledTwice).to.equal(true);
    expect(deleteObjectsStub.callCount).to.equal(2);
    expect(deleteObjectsStub.calledTwice).to.equal(true);

    // Check first call
    const firstCallParams = deleteObjectsStub.firstCall.args[0];
    expect(firstCallParams.Delete.Objects).to.deep.equal([
      { Key: 'sitemaps/invalid1.xml' }
    ]);
    // Check second call
    const secondCallParams = deleteObjectsStub.secondCall.args[0];
    expect(secondCallParams.Delete.Objects).to.deep.equal([
      { Key: 'sitemaps/invalid2.xml' }
    ]);
  });

  after(() => {
    // Ensure all stubs are restored
    sinon.restore();
  });
});


describe('confidence score', async () => {
  beforeEach(async () => {
    const profiles = [
      {
        //profile with same Boo type and total vote count more than 40 and PDB confidence 80
        id: 1, sort: 5, name: 'Pikachu', mbti: 'INFJ', subcategories: [3], enneagram: '1w2', horoscope: 'Taurus', image: 'pikachu.jpg',
        pdb_confidence: 80,  pdb_mbti: "INFJ",pdb_enneagram: "1w2", pdb_horoscope: "Taurus",
        vote: {
          totalCount:100,
          mbti: { INFJ: 38, ENTP: 17 },//total 55
          horoscope: { Taurus: 38, Pisces: 17 },
          enneagram: { "1w2": 38, "8w9": 17 }
        },
      },
      {
        //profile with different Boo type and total vote count more than 40 and PDB confidence 80
        id: 2, sort: 5, name: 'Pikachu', mbti: 'INFJ', subcategories: [3], enneagram: '1w2', horoscope: 'Taurus', image: 'pikachu.jpg',
        pdb_confidence: 80,  pdb_mbti: "ENTP",pdb_enneagram: "8w9", pdb_horoscope: "Pisces",
        vote: {
          totalCount:100,
          mbti: { INFJ: 38, ENTP: 17 },//total 55
          horoscope: { Taurus: 38, Pisces: 17 },
          enneagram: { "1w2": 38, "8w9": 17 }
        },
      },
      {
        //profile with no PDB type and total vote count more than 40
        id: 3, sort: 5, name: 'Pikachu', mbti: 'INFJ', subcategories: [3], enneagram: '1w2', horoscope: 'Taurus', image: 'pikachu.jpg',
        vote: {
          totalCount:100,
          mbti: { INFJ: 38, ENTP: 17 },//total 55
          horoscope: { Taurus: 38, Pisces: 17 },
          enneagram: { "1w2": 38, "8w9": 17 }
        },
      },
      {
        //profile with same Boo type and total vote count less than 10 and PDB confidence 50
        id: 4, sort: 5, name: 'Pikachu', mbti: 'INFJ', subcategories: [3], enneagram: '1w2', horoscope: 'Taurus', image: 'pikachu.jpg',
        pdb_confidence: 50, pdb_mbti: "INFJ", pdb_enneagram: "1w2", pdb_horoscope: "Taurus",
        vote: {
          totalCount:8,
          mbti: { INFJ: 4, ENTP: 4 },//total 8
          horoscope: { Taurus: 4, Pisces: 4 },
          enneagram: { "1w2": 4, "8w9": 4 }
        },
      },
      {
        //profile with different Boo type and total vote count less than 10 and PDB confidence 50
        id: 5, sort: 5, name: 'Pikachu', mbti: 'INFJ', subcategories: [3], enneagram: '1w2', horoscope: 'Taurus', image: 'pikachu.jpg',
        pdb_confidence: 50, pdb_mbti: "ENTP", pdb_enneagram: "8w9", pdb_horoscope: "Pisces",
        vote: {
          totalCount:8,
          mbti: { INFJ: 4, ENTP: 4 },//total 8
          horoscope: { Taurus: 4, Pisces: 4 },
          enneagram: { "1w2": 4, "8w9": 4 }
        },
      },
      {
        //profile with no PDB type and total vote count less than 10
        id: 6, sort: 5, name: 'Pikachu', mbti: 'INFJ', subcategories: [3], enneagram: '1w2', horoscope: 'Taurus', image: 'pikachu.jpg',
        vote: {
          totalCount:8,
          mbti: { INFJ: 4, ENTP: 4 },//total 8
          horoscope: { Taurus: 4, Pisces: 4 },
          enneagram: { "1w2": 4, "8w9": 4 }
        },
      },
      {
        //profile with same Boo type and no vote and PDB confidence 20
        id: 7, sort: 5, name: 'Pikachu', mbti: 'INFJ', subcategories: [3], enneagram: '1w2', horoscope: 'Taurus', image: 'pikachu.jpg',
        pdb_confidence: 20,  pdb_mbti: "INFJ",pdb_enneagram: "1w2", pdb_horoscope: "Taurus",
        vote: {
        },
      },
      {
        //profile with different Boo type and no vote and PDB confidence 20
        id: 8, sort: 5, name: 'Pikachu', mbti: 'ISFP', subcategories: [3], enneagram: '9w8', horoscope: 'Taurus', image: 'pikachu.jpg',
        pdb_confidence: 20, pdb_mbti: "ENTP", pdb_enneagram: "8w9", pdb_horoscope: "Pisces",
        vote: {
        },
      },
      {
        //profile with no PDB type no zodiac no enneagram in profile and no vote
        id: 9, sort: 5, name: 'Pikachu', mbti: 'ISFJ', subcategories: [3], image: 'pikachu.jpg',
        vote: {
        },
      },
      {
        //profile with no vote and no pdb type
        id: 10, sort: 5, name: 'Pikachu', mbti: 'ESFJ', subcategories: [3], enneagram: '4w3', horoscope: 'Taurus',  image: 'pikachu.jpg',
      },
      {
        //profile with no vote ,no pdb type and no boo type
        id: 11, sort: 5, name: 'Pikachu', subcategories: [3], image: 'pikachu.jpg',
      },
      {
        //profile with different Boo type and no vote and no PDB confidence
        id: 12, sort: 5, name: 'Pikachu', mbti: 'ISFP', subcategories: [3], enneagram: '9w8', horoscope: 'Taurus', image: 'pikachu.jpg',
        pdb_mbti: "ENTP", pdb_enneagram: "8w9", pdb_horoscope: "Pisces",
        vote: {
        },
      },
      {
        //profile with same Boo type and no vote and no PDB confidence and no horoscope
        id: 13, sort: 5, name: 'Pikachu', mbti: 'ISFP', subcategories: [3], enneagram: '8w7',  image: 'pikachu.jpg',
        pdb_mbti: "ISFP", pdb_enneagram: "8w7",
        vote: {
        },
      },
      {
        //profile with no Boo type and no vote and pdb confidence 50
        id: 14, sort: 5, name: 'Pikachu', mbti: 'ISFP', subcategories: [3], enneagram: '8w7', horoscope: 'Taurus', image: 'pikachu.jpg',
        pdb_confidence: 50,
        vote: {
        },
      },
      {
        // profile with same Boo type and total vote count between 10 and 19 and PDB confidence 70
        id: 15, sort: 5, name: 'Pikachu', mbti: 'INFJ', subcategories: [3], enneagram: '1w2', horoscope: 'Taurus', image: 'pikachu.jpg',
        pdb_confidence: 70, pdb_mbti: "INFJ", pdb_enneagram: "1w2", pdb_horoscope: "Taurus",
        vote: {
          totalCount: 15,
          mbti: { INFJ: 7, ENTP: 8 }, // total 15
          horoscope: { Taurus: 7, Pisces: 8 },
          enneagram: { "1w2": 7, "8w9": 8 }
        },
      },
      {
        // profile with different Boo type and total vote count between 10 and 19 and PDB confidence 70
        id: 16, sort: 5, name: 'Pikachu', mbti: 'INFJ', subcategories: [3], enneagram: '1w2', horoscope: 'Taurus', image: 'pikachu.jpg',
        pdb_confidence: 70, pdb_mbti: "ENTP", pdb_enneagram: "8w9", pdb_horoscope: "Pisces",
        vote: {
          totalCount: 15,
          mbti: { INFJ: 7, ENTP: 8 }, // total 15
          horoscope: { Taurus: 7, Pisces: 8 },
          enneagram: { "1w2": 7, "8w9": 8 }
        },
      },
      {
        // profile with no PDB type and total vote count between 10 and 19
        id: 17, sort: 5, name: 'Pikachu', mbti: 'INFJ', subcategories: [3], enneagram: '1w2', horoscope: 'Taurus', image: 'pikachu.jpg',
        vote: {
          totalCount: 15,
          mbti: { INFJ: 7, ENTP: 8 }, // total 15
          horoscope: { Taurus: 7, Pisces: 8 },
          enneagram: { "1w2": 7, "8w9": 8 }
        },
      },
      {
        // profile with same Boo type and total vote count between 20 and 39 and PDB confidence 90
        id: 18, sort: 5, name: 'Pikachu', mbti: 'INFJ', subcategories: [3], enneagram: '1w2', horoscope: 'Taurus', image: 'pikachu.jpg',
        pdb_confidence: 90, pdb_mbti: "INFJ", pdb_enneagram: "1w2", pdb_horoscope: "Taurus",
        vote: {
          totalCount: 30,
          mbti: { INFJ: 15, ENTP: 15 }, // total 30
          horoscope: { Taurus: 15, Pisces: 15 },
          enneagram: { "1w2": 15, "8w9": 15 }
        },
      },
      {
        // profile with different Boo type and total vote count between 20 and 39 and PDB confidence 90
        id: 19, sort: 5, name: 'Pikachu', mbti: 'INFJ', subcategories: [3], enneagram: '1w2', horoscope: 'Taurus', image: 'pikachu.jpg',
        pdb_confidence: 90, pdb_mbti: "ENTP", pdb_enneagram: "8w9", pdb_horoscope: "Pisces",
        vote: {
          totalCount: 30,
          mbti: { INFJ: 15, ENTP: 15 }, // total 30
          horoscope: { Taurus: 15, Pisces: 15 },
          enneagram: { "1w2": 15, "8w9": 15 }
        },
      },
      {
        // profile with no PDB type and total vote count between 20 and 39
        id: 20, sort: 5, name: 'Pikachu', mbti: 'INFJ', subcategories: [3], enneagram: '1w2', horoscope: 'Taurus', image: 'pikachu.jpg',
        vote: {
          totalCount: 30,
          mbti: { INFJ: 15, ENTP: 15 }, // total 30
          horoscope: { Taurus: 15, Pisces: 15 },
          enneagram: { "1w2": 15, "8w9": 15 }
        },
      }
    ];
    await Profile.insertMany(profiles);
  });
it('verify profile with same Boo type and vote count more than 40 and PDB confidence 80', async () => {
  let res = await request(app)
    .get('/web/database/profile')
    .query({ id: 1, locale: 'de' });
  expect(res.status).to.equal(200);
  expect(res.body.profile.pdb_mbti).to.eq(undefined)
  expect(res.body.profile.pdb_enneagram).to.eq(undefined)
  expect(res.body.profile.pdb_horoscope).to.eq(undefined)
  expect(res.body.profile.pdb_confidence).to.eq(undefined)
  expect(res.body.profile.confidence_score.mbti).to.eql(92);
  expect(res.body.profile.confidence_score.horoscope).to.eql(92);
  expect(res.body.profile.confidence_score.enneagram).to.eql(92);
  expect(res.body.profile.confidence_score.total).to.eql(92);
});

it('verify profile with different Boo type and vote count more than 40 and PDB confidence 80', async () => {
  let res = await request(app)
    .get('/web/database/profile')
    .query({ id: 2, locale: 'de' });
  expect(res.status).to.equal(200);
  expect(res.body.profile.pdb_mbti).to.eq(undefined)
  expect(res.body.profile.pdb_enneagram).to.eq(undefined)
  expect(res.body.profile.pdb_horoscope).to.eq(undefined)
  expect(res.body.profile.pdb_confidence).to.eq(undefined)
  expect(res.body.profile.confidence_score.mbti).to.eql(84);
  expect(res.body.profile.confidence_score.horoscope).to.eql(84);
  expect(res.body.profile.confidence_score.enneagram).to.eql(84);
  expect(res.body.profile.confidence_score.total).to.eql(84);
});

it('verify profile with no PDB type and vote count more than 40', async () => {
  let res = await request(app)
    .get('/web/database/profile')
    .query({ id: 3, locale: 'de' });
  expect(res.status).to.equal(200);
  expect(res.body.profile.pdb_mbti).to.eq(undefined)
  expect(res.body.profile.pdb_enneagram).to.eq(undefined)
  expect(res.body.profile.pdb_horoscope).to.eq(undefined)
  expect(res.body.profile.pdb_confidence).to.eq(undefined)
  expect(res.body.profile.confidence_score.mbti).to.eql(100);
  expect(res.body.profile.confidence_score.horoscope).to.eql(100);
  expect(res.body.profile.confidence_score.enneagram).to.eql(100);
  expect(res.body.profile.confidence_score.total).to.eql(100);
});

it('verify profile with same Boo type and vote count less than 10 and PDB confidence 50', async () => {
  let res = await request(app)
    .get('/web/database/profile')
    .query({ id: 4, locale: 'de' });
  expect(res.status).to.equal(200);
  expect(res.body.profile.pdb_mbti).to.eq(undefined)
  expect(res.body.profile.pdb_enneagram).to.eq(undefined)
  expect(res.body.profile.pdb_horoscope).to.eq(undefined)
  expect(res.body.profile.pdb_confidence).to.eq(undefined)
  expect(res.body.profile.confidence_score.mbti).to.eql(31);
  expect(res.body.profile.confidence_score.horoscope).to.eql(31);
  expect(res.body.profile.confidence_score.enneagram).to.eql(31);
  expect(res.body.profile.confidence_score.total).to.eql(31);
});

it('verify profile with different Boo type and vote count less than 10 and PDB confidence 50', async () => {
  let res = await request(app)
    .get('/web/database/profile')
    .query({ id: 5, locale: 'de' });
    expect(res.status).to.equal(200);
    expect(res.body.profile.pdb_mbti).to.eq(undefined)
    expect(res.body.profile.pdb_enneagram).to.eq(undefined)
    expect(res.body.profile.pdb_horoscope).to.eq(undefined)
    expect(res.body.profile.pdb_confidence).to.eq(undefined)
    expect(res.body.profile.confidence_score.mbti).to.eql(0);
    expect(res.body.profile.confidence_score.horoscope).to.eql(0);
    expect(res.body.profile.confidence_score.enneagram).to.eql(0);
    expect(res.body.profile.confidence_score.total).to.eql(0);

});

it('verify profile with no PDB type and vote count less than 10', async () => {
  let res = await request(app)
    .get('/web/database/profile')
    .query({ id: 6, locale: 'de' });
    expect(res.status).to.equal(200);
    expect(res.body.profile.pdb_mbti).to.eq(undefined)
    expect(res.body.profile.pdb_enneagram).to.eq(undefined)
    expect(res.body.profile.pdb_horoscope).to.eq(undefined)
    expect(res.body.profile.pdb_confidence).to.eq(undefined)
    expect(res.body.profile.confidence_score.mbti).to.eql(13);
    expect(res.body.profile.confidence_score.horoscope).to.eql(13);
    expect(res.body.profile.confidence_score.enneagram).to.eql(13);
    expect(res.body.profile.confidence_score.total).to.eql(13);
});

it('verify profile with same Boo type and total no vote and PDB confidence 20', async () => {
  let res = await request(app)
    .get('/web/database/profile')
    .query({ id: 7, locale: 'de' });
    expect(res.status).to.equal(200);
    expect(res.body.profile.pdb_mbti).to.eq(undefined)
    expect(res.body.profile.pdb_enneagram).to.eq(undefined)
    expect(res.body.profile.pdb_horoscope).to.eq(undefined)
    expect(res.body.profile.pdb_confidence).to.eq(undefined)
    expect(res.body.profile.confidence_score.mbti).to.eql(10);
    expect(res.body.profile.confidence_score.horoscope).to.eql(10);
    expect(res.body.profile.confidence_score.enneagram).to.eql(10);
    expect(res.body.profile.confidence_score.total).to.eql(10);
});

it('verify profile with different Boo type and total no vote and PDB confidence 20', async () => {
  let res = await request(app)
    .get('/web/database/profile')
    .query({ id: 8, locale: 'de' });
    expect(res.status).to.equal(200);
    expect(res.body.profile.pdb_mbti).to.eq(undefined)
    expect(res.body.profile.pdb_enneagram).to.eq(undefined)
    expect(res.body.profile.pdb_horoscope).to.eq(undefined)
    expect(res.body.profile.pdb_confidence).to.eq(undefined)
    expect(res.body.profile.confidence_score.mbti).to.eql(0);
    expect(res.body.profile.confidence_score.horoscope).to.eql(0);
    expect(res.body.profile.confidence_score.enneagram).to.eql(0);
    expect(res.body.profile.confidence_score.total).to.eql(0);
});

it('verify profile with no PDB type no zodiac no enneagram in profile and total no vote', async () => {
  let res = await request(app)
    .get('/web/database/profile')
    .query({ id: 9, locale: 'de' });
    expect(res.status).to.equal(200);
    expect(res.body.profile.pdb_mbti).to.eq(undefined)
    expect(res.body.profile.pdb_enneagram).to.eq(undefined)
    expect(res.body.profile.pdb_horoscope).to.eq(undefined)
    expect(res.body.profile.pdb_confidence).to.eq(undefined)
    expect(res.body.profile.confidence_score.mbti).to.eql(7);
    expect(res.body.profile.confidence_score.horoscope).to.eql(undefined);
    expect(res.body.profile.confidence_score.enneagram).to.eql(undefined);
    expect(res.body.profile.confidence_score.total).to.eql(7);
});

it('verify profile with no vote and no pdb type', async () => {
  let res = await request(app)
    .get('/web/database/profile')
    .query({ id: 10, locale: 'de' });
    expect(res.status).to.equal(200);
    expect(res.body.profile.pdb_mbti).to.eq(undefined)
    expect(res.body.profile.pdb_enneagram).to.eq(undefined)
    expect(res.body.profile.pdb_horoscope).to.eq(undefined)
    expect(res.body.profile.pdb_confidence).to.eq(undefined)
    expect(res.body.profile.confidence_score.mbti).to.eql(6);
    expect(res.body.profile.confidence_score.horoscope).to.eql(4);
    expect(res.body.profile.confidence_score.enneagram).to.eql(4);
    expect(res.body.profile.confidence_score.total).to.eql(5);
});

it('verify profile with no vote ,no pdb type and no boo type', async () => {
  let res = await request(app)
    .get('/web/database/profile')
    .query({ id: 11, locale: 'de' });
    expect(res.status).to.equal(200);
    expect(res.body.profile.pdb_mbti).to.eq(undefined)
    expect(res.body.profile.pdb_enneagram).to.eq(undefined)
    expect(res.body.profile.pdb_horoscope).to.eq(undefined)
    expect(res.body.profile.pdb_confidence).to.eq(undefined)
    expect(res.body.profile.confidence_score.mbti).to.eql(undefined);
    expect(res.body.profile.confidence_score.horoscope).to.eql(undefined);
    expect(res.body.profile.confidence_score.enneagram).to.eql(undefined);
    expect(res.body.profile.confidence_score.total).to.eql(undefined);
});

it('verify profile with different Boo type and no vote and no PDB confidence', async () => {
  let res = await request(app)
    .get('/web/database/profile')
    .query({ id: 12, locale: 'de' });
    expect(res.status).to.equal(200);
    expect(res.body.profile.pdb_mbti).to.eq(undefined)
    expect(res.body.profile.pdb_enneagram).to.eq(undefined)
    expect(res.body.profile.pdb_horoscope).to.eq(undefined)
    expect(res.body.profile.pdb_confidence).to.eq(undefined)
    expect(res.body.profile.confidence_score.mbti).to.eql(4);
    expect(res.body.profile.confidence_score.horoscope).to.eql(4);
    expect(res.body.profile.confidence_score.enneagram).to.eql(6);
    expect(res.body.profile.confidence_score.total).to.eql(5);
});

it('verify profile with no horoscope same Boo type and no vote and no PDB confidence', async () => {
  let res = await request(app)
    .get('/web/database/profile')
    .query({ id: 13, locale: 'de' });
    expect(res.status).to.equal(200);
    expect(res.body.profile.pdb_mbti).to.eq(undefined)
    expect(res.body.profile.pdb_enneagram).to.eq(undefined)
    expect(res.body.profile.pdb_horoscope).to.eq(undefined)
    expect(res.body.profile.pdb_confidence).to.eq(undefined)
    expect(res.body.profile.confidence_score.mbti).to.eql(4);
    expect(res.body.profile.confidence_score.horoscope).to.eql(undefined);
    expect(res.body.profile.confidence_score.enneagram).to.eql(2);
    expect(res.body.profile.confidence_score.total).to.eql(3);
});

it('verify profile with no Boo type and no vote and pdb confidence 50', async () => {
  let res = await request(app)
    .get('/web/database/profile')
    .query({ id: 14, locale: 'de' });
    expect(res.status).to.equal(200);
    expect(res.body.profile.pdb_mbti).to.eq(undefined)
    expect(res.body.profile.pdb_enneagram).to.eq(undefined)
    expect(res.body.profile.pdb_horoscope).to.eq(undefined)
    expect(res.body.profile.pdb_confidence).to.eq(undefined)
    expect(res.body.profile.confidence_score.mbti).to.eql(4);
    expect(res.body.profile.confidence_score.horoscope).to.eql(4);
    expect(res.body.profile.confidence_score.enneagram).to.eql(2);
    expect(res.body.profile.confidence_score.total).to.eql(3);
});
it('verify profile with same Boo type and total vote count between 10 and 19 and PDB confidence 70', async () => {
  let res = await request(app)
    .get('/web/database/profile')
    .query({ id: 15, locale: 'de' });
  expect(res.status).to.equal(200);
  expect(res.body.profile.confidence_score.mbti).to.eql(58);
  expect(res.body.profile.confidence_score.horoscope).to.eql(58);
  expect(res.body.profile.confidence_score.enneagram).to.eql(58);
  expect(res.body.profile.confidence_score.total).to.eql(58);
});

it('verify profile with different Boo type and total vote count between 10 and 19 and PDB confidence 70', async () => {
  let res = await request(app)
    .get('/web/database/profile')
    .query({ id: 16, locale: 'de' });
  expect(res.status).to.equal(200);
  expect(res.body.profile.confidence_score.mbti).to.eql(29);
  expect(res.body.profile.confidence_score.horoscope).to.eql(29);
  expect(res.body.profile.confidence_score.enneagram).to.eql(29);
  expect(res.body.profile.confidence_score.total).to.eql(29);
});

it('verify profile with no PDB type and total vote count between 10 and 19', async () => {
  let res = await request(app)
    .get('/web/database/profile')
    .query({ id: 17, locale: 'de' });
  expect(res.status).to.equal(200);
  expect(res.body.profile.confidence_score.mbti).to.eql(47);
  expect(res.body.profile.confidence_score.horoscope).to.eql(47);
  expect(res.body.profile.confidence_score.enneagram).to.eql(47);
  expect(res.body.profile.confidence_score.total).to.eql(47);
});

it('verify profile with same Boo type and total vote count between 20 and 39 and PDB confidence 90', async () => {
  let res = await request(app)
    .get('/web/database/profile')
    .query({ id: 18, locale: 'de' });
  expect(res.status).to.equal(200);
  expect(res.body.profile.confidence_score.mbti).to.eql(76);
  expect(res.body.profile.confidence_score.horoscope).to.eql(76);
  expect(res.body.profile.confidence_score.enneagram).to.eql(76);
  expect(res.body.profile.confidence_score.total).to.eql(76);
});
it('verify profile with different Boo type and total vote count between 20 and 39 and PDB confidence 90', async () => {
  let res = await request(app)
    .get('/web/database/profile')
    .query({ id: 19, locale: 'de' });
  expect(res.status).to.equal(200);
  expect(res.body.profile.confidence_score.mbti).to.eql(40);
  expect(res.body.profile.confidence_score.horoscope).to.eql(40);
  expect(res.body.profile.confidence_score.enneagram).to.eql(40);
  expect(res.body.profile.confidence_score.total).to.eql(40);
});

it('verify profile with no PDB type and total vote count between 20 and 39', async () => {
  let res = await request(app)
    .get('/web/database/profile')
    .query({ id: 20, locale: 'de' });
  expect(res.status).to.equal(200);
  expect(res.body.profile.confidence_score.mbti).to.eql(63);
  expect(res.body.profile.confidence_score.horoscope).to.eql(63);
  expect(res.body.profile.confidence_score.enneagram).to.eql(63);
  expect(res.body.profile.confidence_score.total).to.eql(63);
});
})

describe('get leadership board', () => {
  let pageSizeStub;
  beforeEach(() => {
    pageSizeStub = sinon.stub(constants, 'getPageSize').returns(2);
  });
  afterEach(() => {
    if (pageSizeStub) {
      pageSizeStub.restore();
    }
  });

  it('get leaders', async () => {
    await initApp(1);

    const getDbLeaders = (params) => request(app)
      .get('/web/database/leaders')
      .query(params);

    // invalidInputError
    res = await getDbLeaders({ page: 'avc' });
    expect(res.status).to.eql(422);

    // success
    res = await getDbLeaders({ page: 0 });
    expect(res.status).to.eql(200);
    expect(res.body.leaders).to.eql([]);// no users as no one has dbUploadKarma > 0

    // setting dbUploadKarma > 0
    function compareLeaderAndMongoDoc(doc, leader) {
      expect(JSON.stringify(doc)).to.eql(JSON.stringify(leader));
    }

    {
      const userDoc = await User.findOneAndUpdate(
        { _id: '1' },
        { $inc: { dbUploadKarmaReceived: 10 } },
        {
          projection: {
            _id: 1, firstName: 1, picture: { $first: '$pictures' }, 'personality.mbti': 1, enneagram: 1, horoscope: 1, numDbUploads: 1, dbUploadCoinsReceived: 1, dbUploadKarmaReceived: 1,
          },
          new: true,
        },
      );

      // success
      res = await getDbLeaders({ page: 0 });
      expect(res.status).to.eql(200);
      expect(res.body.leaders.length).to.eql(1);// user 1 shows up now
      compareLeaderAndMongoDoc(res.body.leaders[0], userDoc);

      // create two more users
      await initApp(2);
      await initApp(3);

      // result same as previous as db upload karma for new users is 0
      res = await getDbLeaders({ page: 0 });
      expect(res.status).to.eql(200);
      expect(res.body.leaders.length).to.eql(1);// user 1 shows up now
      compareLeaderAndMongoDoc(res.body.leaders[0], userDoc);
    }

    /*     function compareLeaderStats(leader, metrics) {
          expect(leader.dbUploadKarmaReceived).to.eql(metrics[0]);
          expect(leader.numDbUploads).to.eql(metrics[1]);
          expect(leader.dbUploadCoinsReceived).to.eql(metrics[2]);
        } */

    const mockDbStats = [
      [10, 2, 10],
      [20, 1, 10],
      [5, 1, 20],
    ];

    const userDocs = [];
    // update users with mock stats
    for (let i = 0; i < 3; i++) {
      userDocs.push(await

      User.findOneAndUpdate(
        { _id: 1 + i },
        {
          $set: {
            dbUploadKarmaReceived: mockDbStats[i][0],
            numDbUploads: mockDbStats[i][0],
            dbUploadCoinsReceived: mockDbStats[i][0],
          },
        },
        {
          projection: {
            _id: 1, firstName: 1, picture: { $first: '$pictures' }, 'personality.mbti': 1, enneagram: 1, horoscope: 1, numDbUploads: 1, dbUploadCoinsReceived: 1, dbUploadKarmaReceived: 1,
          },
          new: true,
        },
      ));
    }

    // results sorted by dbUploadKarmaReceived
    res = await getDbLeaders({ page: 0 });
    expect(res.status).to.eql(200);
    expect(res.body.leaders.length).to.eql(2);// only two results because pagination occurs at 2
    compareLeaderAndMongoDoc(res.body.leaders[0], userDocs[1]);// because user 1 has highest dbuploadkarma
    compareLeaderAndMongoDoc(res.body.leaders[1], userDocs[0]);

    // results sorted by dbUploadKarmaReceived
    res = await getDbLeaders({ page: 1 });
    expect(res.status).to.eql(200);
    expect(res.body.leaders.length).to.eql(1);// only two results because pagination occurs at 2
    compareLeaderAndMongoDoc(res.body.leaders[0], userDocs[2]);

    // no results on next page
    res = await getDbLeaders({ page: 2 });
    expect(res.status).to.eql(200);
    expect(res.body.leaders).to.eql([]);
  });
});

it('tenjin', async () => {
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0)
    .send({ advertisingId: '0' });
  expect(res.status).to.equal(200);
  expect(res.body.user.optOutOfAdTargeting).to.equal();

  res = await request(app)
    .get('/tenjin')
    .query({ advertisingId: '0', adNetwork: 'fb' });
  expect(res.status).to.equal(200);

  user = await User.findOne({ _id: 0 });
  expect(user.advertisingId).to.equal('0');
  expect(user.tenjin.adNetwork).to.equal('fb');

  res = await request(app)
    .put('/v1/user/optOutOfAdTargeting')
    .set('authorization', 0)
    .send({ optOutOfAdTargeting: true });
  expect(res.status).to.equal(200);

  user = await User.findOne({ _id: 0 });
  expect(user.advertisingId).to.equal();

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0)
  expect(res.status).to.equal(200);
  expect(res.body.user.optOutOfAdTargeting).to.equal(true);
});

it('check phone login', async () => {
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 'mockuser_5_testmail3_0002')
  expect(res.status).to.equal(200);

  res = await request(app)
    .put('/web/phoneLogin')
    .send({ phoneNumber: getMockNumber('0002') })
  expect(res.status).to.equal(200);
  expect(res.body.allowed).to.equal(true);

  res = await request(app)
    .put('/web/phoneLogin')
    .send({ phoneNumber: getMockNumber('0001') })
  expect(res.status).to.equal(200);
  expect(res.body.allowed).to.equal(false);

  // ban the user
  await User.updateMany({}, {shadowBanned: true});

  res = await request(app)
    .put('/web/phoneLogin')
    .send({ phoneNumber: getMockNumber('0002') })
  expect(res.status).to.equal(200);
  expect(res.body.allowed).to.equal(false);
});

it('is auth allowed - device id', async () => {
  // banned domains
  res = await request(app)
    .put('/web/isAuthAllowed')
    .send({ deviceId: '0', email: '<EMAIL>' })
  expect(res.status).to.equal(200);
  expect(res.body.allowed).to.equal(false);

  signupRejected = await SignupRejected.findOne().sort('-createdAt');
  expect(signupRejected.checkedBy).to.equal('isAuthAllowed');
  expect(signupRejected.email).to.equal('<EMAIL>');
  expect(signupRejected.deviceId).to.equal('0');
  expect(signupRejected.reason).to.equal('disposable email');

  res = await request(app)
    .put('/web/isAuthAllowed')
    .send({ deviceId: '0', email: '<EMAIL>' })
  expect(res.status).to.equal(200);
  expect(res.body.allowed).to.equal(false);

  // no accounts exist yet
  res = await request(app)
    .put('/web/isAuthAllowed')
    .send({ deviceId: '0', email: '<EMAIL>' })
  expect(res.status).to.equal(200);
  expect(res.body.allowed).to.equal(true);

  // create 5 accounts with same device id
  for (let i = 0; i < 5; i++) {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', `mockuser_${i}_testmail${i}_000${i}`)
      .send({deviceId: '0'})
    expect(res.status).to.equal(200);
  }

  // check email that belongs to device id
  res = await request(app)
    .put('/web/isAuthAllowed')
    .send({ deviceId: '0', email: getMockEmail('testmail2') })
  expect(res.status).to.equal(200);
  expect(res.body.allowed).to.equal(true);

  // check email that does not belong to device id
  res = await request(app)
    .put('/web/isAuthAllowed')
    .send({ deviceId: '0', email: getMockEmail('testmail7') })
  expect(res.status).to.equal(200);
  expect(res.body.allowed).to.equal(false);

  signupRejected = await SignupRejected.findOne().sort('-createdAt');
  expect(signupRejected.email).to.equal(getMockEmail('testmail7'));
  expect(signupRejected.deviceId).to.equal('0');
  expect(signupRejected.reason).to.equal('device id');

  // check different device id
  res = await request(app)
    .put('/web/isAuthAllowed')
    .send({ deviceId: '1', email: getMockEmail('testmail7') })
    .set('X-Forwarded-For', '************')
  expect(res.status).to.equal(200);
  expect(res.body.allowed).to.equal(true);
});

it('is auth allowed - ip address', async () => {

  let clock = sinon.useFakeTimers();

  const ip1 = '************';
  const ip2 = '************';

  // no accounts exist yet
  res = await request(app)
    .put('/web/isAuthAllowed')
    .send({ email: '<EMAIL>' })
    .set('X-Forwarded-For', ip1)
  expect(res.status).to.equal(200);
  expect(res.body.allowed).to.equal(true);

  // create 5 accounts with same ip
  for (let i = 0; i < 5; i++) {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', `mockuser_${i}_testmail${i}_000${i}`)
      .set('X-Forwarded-For', ip1)
    expect(res.status).to.equal(200);
  }

  // check email that belongs to ip
  res = await request(app)
    .put('/web/isAuthAllowed')
    .send({ email: getMockEmail('testmail2') })
    .set('X-Forwarded-For', ip1)
  expect(res.status).to.equal(200);
  expect(res.body.allowed).to.equal(true);

  // check email that does not belong to ip
  res = await request(app)
    .put('/web/isAuthAllowed')
    .send({ deviceId: '0', email: getMockEmail('testmail7') })
    .set('X-Forwarded-For', ip1)
  expect(res.status).to.equal(200);
  expect(res.body.allowed).to.equal(false);

  signupRejected = await SignupRejected.findOne().sort('-createdAt');
  expect(signupRejected.email).to.equal(getMockEmail('testmail7'));
  expect(signupRejected.deviceId).to.equal('0');
  expect(signupRejected.ip).to.equal(ip1);
  expect(signupRejected.reason).to.equal('ip address');

  // check different ip
  res = await request(app)
    .put('/web/isAuthAllowed')
    .send({ email: getMockEmail('testmail7') })
    .set('X-Forwarded-For', ip2)
  expect(res.status).to.equal(200);
  expect(res.body.allowed).to.equal(true);

  // 1 day
  clock.tick(1 * 24 * 3600 * 1000);

  // should be allowed now
  res = await request(app)
    .put('/web/isAuthAllowed')
    .send({ email: getMockEmail('testmail7') })
    .set('X-Forwarded-For', ip1)
  expect(res.status).to.equal(200);
  expect(res.body.allowed).to.equal(true);

  clock.restore();
});

it('web visitor config', async () => {
  res = await request(app)
    .put('/web/visitor')
    .send({ webDeviceId: '0', locale: 'en' });
  expect(res.status).to.equal(200);
  expect(res.body.config.social_proof_signed_up).to.equal(true);
  expect(res.body.config.show_login_timer).to.equal(false);
  expect(res.body.config.redirect_mobile_to_app_store_v2).to.equal(true);
  expect(res.body.config.get_app_button).to.equal(false);
  expect(res.body.config.move_universe_intro_paragraph).to.equal(true);
  expect(res.body.config.web_90).to.equal();
  expect(res.body.config.web_103).to.equal(true);

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0)
    .send({ webDeviceId: '0' });
  expect(res.status).to.equal(200);

  visitor = await WebVisitor.findOne({ webDeviceId: '0' });
  expect(visitor.events.signed_up).to.equal(true);
  expect(visitor.user).to.equal('0');
});

it('assign web visitor config to user after signup', async () => {

  // web config not assigned
  res = await request(app)
    .put('/web/visitor')
    .send({ webDeviceId: '0' });
  expect(res.status).to.equal(200);

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0)
    .send({ webDeviceId: '0' });
  expect(res.status).to.equal(200);

  user = await User.findById('0');
  expect(user.config.test_config).to.equal();

  await mongoose.connection.db.dropDatabase();

  // web config assigned to true
  res = await request(app)
    .put('/web/visitor')
    .send({ webDeviceId: '0' });
  expect(res.status).to.equal(200);

  visitor = await WebVisitor.findOne({ webDeviceId: '0' });
  visitor.config.test_config = true;
  await visitor.save();

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0)
    .send({ webDeviceId: '0' });
  expect(res.status).to.equal(200);

  user = await User.findById('0');
  expect(user.config.test_config).to.equal(true);

  await mongoose.connection.db.dropDatabase();

  // web config assigned to false
  res = await request(app)
    .put('/web/visitor')
    .send({ webDeviceId: '0' });
  expect(res.status).to.equal(200);

  visitor = await WebVisitor.findOne({ webDeviceId: '0' });
  visitor.config.test_config = false;
  await visitor.save();

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0)
    .send({ webDeviceId: '0' });
  expect(res.status).to.equal(200);

  user = await User.findById('0');
  expect(user.config.test_config).to.equal(false);
});

it('app visitor country', async () => {
  const ip = '************';

  res = await request(app)
    .put('/web/appVisitor')
    .set('X-Forwarded-For', ip)
  expect(res.status).to.equal(200);
  expect(res.body.countryCode).to.equal('JP');
});

it('app visitor config', async () => {
  res = await request(app)
    .put('/web/appVisitor')
    .send({ deviceId: '0', locale: 'en', appVersion: '1.13.68' });
  expect(res.status).to.equal(200);

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0)
    .send({ deviceId: '0' });
  expect(res.status).to.equal(200);

  visitor = await AppVisitor.findOne({ deviceId: '0' });
  expect(visitor.events.signed_up).to.equal(true);
  expect(visitor.appVersion).to.equal('1.13.68');
});

it('assign app visitor config to user after signup', async () => {

  // app visitor config not assigned
  res = await request(app)
    .put('/web/appVisitor')
    .send({ deviceId: '0' });
  expect(res.status).to.equal(200);

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0)
    .send({ deviceId: '0' });
  expect(res.status).to.equal(200);

  user = await User.findById('0');
  expect(user.config.test_config).to.equal();

  await mongoose.connection.db.dropDatabase();

  // app visitor config assigned to true
  res = await request(app)
    .put('/web/appVisitor')
    .send({ deviceId: '0' });
  expect(res.status).to.equal(200);

  visitor = await AppVisitor.findOne({ deviceId: '0' });
  visitor.config.test_config = true;
  await visitor.save();

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0)
    .send({ deviceId: '0' });
  expect(res.status).to.equal(200);

  user = await User.findById('0');
  expect(user.config.test_config).to.equal(true);

  await mongoose.connection.db.dropDatabase();

  // app visitor config assigned to false
  res = await request(app)
    .put('/web/appVisitor')
    .send({ deviceId: '0' });
  expect(res.status).to.equal(200);

  visitor = await AppVisitor.findOne({ deviceId: '0' });
  visitor.config.test_config = false;
  await visitor.save();

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0)
    .send({ deviceId: '0' });
  expect(res.status).to.equal(200);

  user = await User.findById('0');
  expect(user.config.test_config).to.equal(false);
});

it('app visitor configs', async () => {
  // finalized configs
  res = await request(app)
    .put('/web/appVisitor')
    .send({ deviceId: '0', locale: 'en', appVersion: '1.13.68' });
  expect(res.status).to.equal(200);
  expect(res.body.config.use_bunny_cdn).to.equal(true);
  expect(res.body.config.app_531).to.equal(true);
  expect(res.body.config.app_554).to.equal(false);
  expect(res.body.config.app_595).to.equal(false);
  expect(res.body.config.app_834).to.equal(true);

  await mongoose.connection.db.dropDatabase();

  // experimental configs
  res = await request(app)
    .put('/web/appVisitor')
    .send({ deviceId: '0', locale: 'en', appVersion: '1.13.92' });
  expect(res.status).to.equal(200);

  await mongoose.connection.db.dropDatabase();

  res = await request(app)
    .put('/web/appVisitor')
    .send({ deviceId: '0', locale: 'en', appVersion: '1.13.93' });
  expect(res.status).to.equal(200);

  await mongoose.connection.db.dropDatabase();

  res = await request(app)
    .put('/web/appVisitor')
    .send({ deviceId: '0', locale: 'en', appVersion: '1.13.94' });
  expect(res.status).to.equal(200);
});

it('unsubscribe email', async () => {
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0)
    .send({ webDeviceId: '0' });
  expect(res.status).to.equal(200);
  expect(res.body.user.pushNotificationSettings.email).to.equal(true);

  res = await request(app)
    .put('/web/unsubscribe-email')
    .query({ email: '<EMAIL>' });
  expect(res.status).to.equal(404);

  const hash = emailUnsubLib.unsubHash('0', '<EMAIL>');
  console.log(hash);
  res = await request(app)
    .put('/web/unsubscribe-email')
    .query({ email: '<EMAIL>', hash });
  expect(res.status).to.equal(200);

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0)
    .send({ webDeviceId: '0' });
  expect(res.status).to.equal(200);
  expect(res.body.user.pushNotificationSettings.email).to.equal(false);
});

it('url encode email in unsubscribe link', async () => {
  const user = {
    _id: '0',
    email: '<EMAIL>',
    locale: 'en',
  };
  const text = emailLib.getUnsubText(user);
  console.log(text);
  expect(text).to.include('https://boo.world/unsubscribe?email=test%2Bboo%40boo.world');
});

it('countries in profiles', async () => {
  const profiles = [
    {
      id: 1, sort: 5, name: 'Pikachu', mbti: 'INFJ', subcategories: [3], enneagram: '1w2', horoscope: 'Taurus', image: 'pikachu.jpg', countries: ['Japan']
    },
  ];
  await Profile.insertMany(profiles);

  res = await request(app)
    .get('/web/database/profile')
    .query({ id: 1 });
  expect(res.status).to.equal(200);
  expect(res.body.profile.countries).to.eql(['Japan']);
});

describe('profile lastUpdated add', () => {
  let profile;

  beforeEach(async () => {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    await Profile.deleteMany({});
    await Comment.deleteMany({});
    profile = await Profile.create({
      id: 1,
      name: 'Naruto',
    });
  });

  it('should return lastUpdated', async () => {

    let res = await request(app)
      .get('/web/database/profile')
      .query({ id: 1 });
    expect(res.status).to.equal(200);
    const lastUpdated = new Date(res.body.profile.lastUpdated);
    expect(lastUpdated).to.be.a('date');
    expect(lastUpdated).to.be.lessThan(new Date());
  });

  it('should set lastUpdated to the current date for create', async () => {
    const newProfile = await Profile.findOne({ name: 'Naruto' });
    expect(newProfile.lastUpdated).to.be.a('date');
    expect(newProfile.lastUpdated).to.be.lessThan(new Date());
  });

    it('should set lastUpdated to the current date by default', async () => {
      const newProfile = new Profile({ name: 'Sasuke' });
      await newProfile.save();
      await new Promise((resolve) => setTimeout(resolve, 1))
      expect(newProfile.lastUpdated).to.be.a('date');
      expect(newProfile.lastUpdated).to.be.lessThan(new Date());
    });

    it('should update lastUpdated when .save is called', async () => {
      const initialLastUpdated = profile.lastUpdated;
      await new Promise((r) => setTimeout(r, 5));
      profile.name = 'Naruto Uzumaki';
      profile.vote['totalCount'] = 100
      await profile.save();
      expect(profile.lastUpdated).to.be.a('date');
      expect(profile.lastUpdated).to.be.greaterThan(initialLastUpdated);
    });

    it('should update lastUpdated when a comment is posted', async () => {
      const newProfile = await Profile.findOne({ name: 'Naruto' });
      const initialLastUpdated = newProfile.lastUpdated;
      // Post a comment with vote
      const res = await request(app)
        .post('/v1/comment')
        .set('authorization', 0)
        .send({
          parentId: profile._id,
          vote: { mbti: 'INTJ' },
        });
        expect(res.status).to.equal(200);
      const updatedProfile = await Profile.findById(profile._id);
      expect(updatedProfile.lastUpdated).to.be.a('date');
      expect(updatedProfile.lastUpdated).to.be.lessThan(new Date());
      expect(updatedProfile.lastUpdated).to.be.greaterThan(initialLastUpdated);
    });
    it('should update lastUpdated when sort and other fields is modified', async () => {
      const newProfile = await Profile.findOne({ name: 'Naruto' });
      const initialLastUpdated = newProfile.lastUpdated;
      profile.sort = Math.random();
      profile.name = 'Naruto part 2'
      await profile.save();
      const updatedProfile = await Profile.findById(profile._id);
      expect(updatedProfile.lastUpdated).to.be.greaterThan(initialLastUpdated);
    });
    it('should not update lastUpdated when only sort field is modified', async () => {
      const newProfile = await Profile.findOne({ name: 'Naruto' });
      const initialLastUpdated = newProfile.lastUpdated;
      profile.sort = Math.random();
      await profile.save();
      const updatedProfile = await Profile.findById(profile._id);
      expect(updatedProfile.lastUpdated.getTime()).to.equal(initialLastUpdated.getTime());
    });
});

it('should return linkedPillarKeywords and translatedNames', async () => {

  let res = await request(app)
  .put('/v1/user/initApp')
  .set('authorization', 0);
  expect(res.status).to.equal(200);
  profile = await Profile.create({
    id: 1,
    name: 'Naruto',
    translatedNames: {
    en: 'Naruto',
    ja: 'ナルト',
    },
    linkedPillarKeywords: {
      en: [
        {
          url: '/resources/navigating-confusing-world-sam-harris-insights',
          keyword: 'make sense of a complex situation',
        }
      ],
      fr: [
        {
          url: '/resources/navigating-confusing-world-sam-harris-insights-fr',
          keyword: 'fr make sense of a complex situation',
        }
      ]
    }
  });

  res = await request(app)
    .get('/web/database/profile')
    .query({ id: 1 });
  expect(res.status).to.equal(200);
  expect(res.body.profile.linkedPillarKeywords).to.eql({
    en: [
    {
      url: '/resources/navigating-confusing-world-sam-harris-insights',
      keyword: 'make sense of a complex situation'
    }
  ]})
  expect(res.body.profile.translatedNames).to.eql({ en: 'Naruto' })

  res = await request(app)
    .get('/web/database/profile')
    .query({ id: 1, locale: 'fr' });
  expect(res.status).to.equal(200);
  expect(res.body.profile.linkedPillarKeywords).to.eql({
    fr: [
    {
      url: '/resources/navigating-confusing-world-sam-harris-insights-fr',
      keyword: 'fr make sense of a complex situation'
    }
  ]})
  expect(res.body.profile.translatedNames).to.eql({})

  res = await request(app)
    .get('/web/database/profile')
    .query({ id: 1, locale: 'ja' });
  expect(res.status).to.equal(200);
  expect(res.body.profile.linkedPillarKeywords).to.eql({})
  expect(res.body.profile.translatedNames).to.eql({ ja: 'ナルト' })
});

it('should return questions related to profile title', async () => {

  profile = await Profile.insertMany([
    { id: 1, name: 'Naruto', horoscope: 'Virgo', mbti: 'ENTJ' },
    { id: 2, name: 'Naruto Sakura', horoscope: 'Virgo', mbti: 'ENTJ' },
    { id: 3, name: 'Dong', horoscope: 'Virgo', mbti: 'ENTJ' },
    { id: 4, name: 'Ross', subcategories: [29] , horoscope: 'Virgo', mbti: 'ENTJ' }

]);
  for (let i = 0; i < 3; i++) {
    res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', i);
    expect(res.status).to.equal(200);
    kpopId = res.body.interests[0]._id;
  }

  for (let i = 0; i < 10; i++) {
    let text = `text No ${i}`
    let title = `title No ${i}`

    if (i == 0) title = 'Joe and Biden is a US citizen'
    if (i == 1) title = 'Joi Biden is a US citizen'
    if (i == 2) title = 'Joi Bidens is a US citizen'
    if (i == 3) title = 'Joe Biden is a US citizen'
    if (i == 4) title = 'Joe is a US citizen'
    if (i == 5) title = 'Biden is a US citizen'
    if (i == 6) title = 'Joi is a US citizen'
    if (i == 7) title = 'Bidyn is a US citizen'
    if (i == 8) title = 'P is a US citizen, friends show'
    if (i == 9) text = 'Joe Biden is a us citizen friends hello'


    res = await request(app)
    .post('/v1/question')
    .set('authorization', i < 6 ? 0 : 1)
    .send({
      interestId: kpopId,
      title,
      text,
    });

    res = await request(app)
      .get('/web/question/profileNameRelated')
    expect(res.status).to.equal(404)

    res = await request(app)
      .get('/web/question/profileNameRelated')
      .query({ profileId: 2321 })
    expect(res.status).to.equal(404)

    res = await request(app)
      .get('/web/question/profileNameRelated')
      .query({ profileId: 3 })
    expect(res.status).to.equal(200)
    expect(res.body.questions.length).to.be.at.least(1);

    res = await request(app)
    .get('/web/question/profileSubcategoryRelated')
  expect(res.status).to.equal(404)

  res = await request(app)
    .get('/web/question/profileSubcategoryRelated')
    .query({ profileId: 2321 })
  expect(res.status).to.equal(404)


  res = await request(app)
  .get('/web/question/profileSubcategoryRelated')
  .query({ profileId: 3 })
expect(res.status).to.equal(404)

  res = await request(app)
    .get('/web/question/profileSubcategoryRelated')
    .query({ profileId: 4 })
  expect(res.status).to.equal(200)
  expect(res.body.questions.length).to.be.at.least(1);
  }
});



describe('handle zodiac based on birthday of a profile', async () => {
  beforeEach(async () => {
    await Profile.insertMany([
      {
        id: 1, name: 'Naruto', birthday: new Date('2024-08-29'), horoscope: 'Leo', mbti: 'ENTJ'
      },
      {
        id: 2, name: 'Naruto Sakura', horoscope: 'Taurus', mbti: 'ENTJ',
      },
      {
        id: 3, name: 'Naruto Sakura II', horoscope: 'Cancer', mbti: 'ENTJ', birthday: new Date('2024-08-27'),
        vote: {
          totalCount:100,
          mbti: { INFJ: 38, ENTP: 17 },//total 55
          horoscope: { Taurus: 38, Pisces: 17, Virgo: 19 },
          enneagram: { "1w2": 38, "8w9": 17 }
        },
      },
      {
        id: 4, name: 'Naruto Sakura III', horoscope: 'Cancer', mbti: 'ENTJ',
      },
  ]);

  for (let id = 0; id < 8; id++) {
    let res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', id);
    expect(res.status).to.equal(200);
  }

  });

  it('set horoscope according to birthday', async () => {

    res = await request(app)
      .get('/web/database/profile')
      .query({ id: 1 });
    expect(res.status).to.equal(200);
    expect(res.body.profile.confidence_score.horoscope).to.eql(100)

    profile = await Profile.findOne({ id : 1 })

    res = await request(app)
    .post('/v1/comment')
    .set('authorization', 0)
    .send({
      parentId: profile._id,
      vote: { mbti: 'ENTJ', horoscope: 'Aries' },
    });
    expect(res.status).to.equal(200);

    profile = await Profile.findOne({ id : 1 })
    expect(profile.mbti).to.equal('ENTJ');
    expect(profile.horoscope).to.equal('Virgo');

    res = await request(app)
    .get('/web/database/profile')
    .query({ id: 1 });
    expect(res.status).to.equal(200);
    expect(res.body.profile.vote).to.eql({ totalCount: 1, mbti: { ENTJ: 1 } })
    expect(res.body.profile.confidence_score.horoscope).to.eql(100)
    expect(res.body.profile.confidence_score.mbti).to.eql(25)
    expect(res.body.profile.confidence_score.total).to.eql(63)
  });

  it('should not change horoscope after a 5 vote', async () => {
    profile = await Profile.findOne({ id : 2 })
    res = await request(app)
    .post('/v1/comment')
    .set('authorization', 0)
    .send({
      parentId: profile._id,
      vote: { mbti: 'ESFP', horoscope: 'Leo' },
    });
    expect(res.status).to.equal(200);

    for(let i = 1; i < 7; i++){
      res = await request(app)
      .post('/v1/comment')
      .set('authorization', i)
      .send({
        parentId: profile._id,
        vote: { mbti: 'ESFP', horoscope: 'Virgo', text: 'check' },
      });
      expect(res.status).to.equal(200);
    }

    res = await request(app)
    .get('/web/database/profile')
    .query({ id: 2 });
    expect(res.status).to.equal(200);
    expect(res.body.profile.vote).to.eql({ totalCount: 7, horoscope: { Leo: 1, Virgo: 6 }, mbti: { ESFP: 7 } })
    expect(res.body.profile.horoscope).to.eql('Virgo')
    expect(res.body.profile.confidence_score.horoscope).to.eql(21)
    expect(res.body.profile.confidence_score.mbti).to.eql(25)
    expect(res.body.profile.confidence_score.total).to.eql(23)

    profile = await Profile.findOne({ id : 3 })

    for (let i = 1; i < 7; i++) {
      res = await request(app)
      .post('/v1/comment')
      .set('authorization', i)
      .send({
        parentId: profile._id,
        vote: { mbti: 'ESFP' , horoscope: 'Leo', text: 'check' },
      });
      expect(res.status).to.equal(200);

      res = await request(app)
      .get('/web/database/profile')
      .query({ id: 3 });
      expect(res.status).to.equal(200);
      expect(res.body.profile.horoscope).to.eql('Virgo')
      expect(res.body.profile.confidence_score.horoscope).to.eql(100)
    }

  });

  it('handle old comments of profiles having birthday with horoscope', async () => {

  profile = await Profile.findOne({ id : 4 })

  res = await request(app)
  .post('/v1/comment')
  .set('authorization', 0)
  .send({
    parentId: profile._id,
    vote: { mbti: 'ESFP', horoscope : 'Virgo' },
  });
  expect(res.status).to.equal(200);

  res = await request(app)
  .post('/v1/comment')
  .set('authorization', 1)
  .send({
    parentId: profile._id,
    vote: { horoscope : 'Cancer' },
  });
  expect(res.status).to.equal(200);

  res = await request(app)
  .post('/v1/comment')
  .set('authorization', 2)
  .send({
    parentId: profile._id,
    vote: { mbti: 'ESFP', enneagram: '1w2' },
  });
  expect(res.status).to.equal(200);

  res = await request(app)
  .post('/v1/comment')
  .set('authorization', 3)
  .send({
    parentId: profile._id,
    vote: { enneagram: '1w2' },
  });
  expect(res.status).to.equal(200);

  res = await request(app)
  .post('/v1/comment')
  .set('authorization', 4)
  .send({
    parentId: profile._id,
    vote: { mbti: 'ESFP' },
  });
  expect(res.status).to.equal(200);

  res = await request(app)
  .post('/v1/comment')
  .set('authorization', 5)
  .send({
    parentId: profile._id,
    text: 'check',
    vote: { horoscope : 'Leo' },
  });
  expect(res.status).to.equal(200);

  res = await request(app)
  .post('/v1/comment')
  .set('authorization', 6)
  .send({
    parentId: profile._id,
    gif: 'https://media.tenor.com/4w937X8-7aUAAAAM/mcdonalds-grimace.gif',
    vote: { horoscope : 'Aries', enneagram: '1w2' },
  });
  expect(res.status).to.equal(200);

  res = await request(app)
  .post('/v1/comment')
  .set('authorization', 7)
  .send({
    parentId: profile._id,
    gif: 'https://media.tenor.com/4w937X8-7aUAAAAM/mcdonalds-grimace.gif',
    vote: { horoscope : 'Aries' },
  });
  expect(res.status).to.equal(200);

  res = await request(app)
  .get('/v1/comment')
  .query({ profileId: profile._id.valueOf() })
  .set('authorization', 0);
  expect(res.status).to.equal(200);

  comments = res.body.comments.map(x => {
    const result = {};
    if (x.vote) {
      result.vote = x.vote;
    }
    if (x.text || x.text == '') {
      result.text = x.text;
    }
    if (x.gif) {
      result.gif = x.gif;
    }
    return result;
  });
   expect(comments).to.eql([
      { vote: { horoscope: 'Aries', }, 'text': '', gif: 'https://media.tenor.com/4w937X8-7aUAAAAM/mcdonalds-grimace.gif' },
      { vote: { horoscope: 'Aries', enneagram: '1w2' }, 'text': '', gif: 'https://media.tenor.com/4w937X8-7aUAAAAM/mcdonalds-grimace.gif' },
      { vote: { horoscope: 'Leo' }, 'text': 'check' },
      { vote: { mbti: 'ESFP' }, 'text': '' },
      { vote: { enneagram: '1w2' }, 'text': '' },
      { vote: { mbti: 'ESFP', enneagram: '1w2' }, 'text': '' },
      { vote: { horoscope: 'Cancer' }, 'text': '' },
      { vote: { mbti: 'ESFP', horoscope: 'Virgo' }, 'text': '' },
    ]);

  res = await request(app)
  .get('/web/database/profile')
  .query({ id: 4 });
  expect(res.status).to.equal(200);
  expect(res.body.profile.horoscope).to.eql('Aries')
  expect(res.body.profile.confidence_score.horoscope).to.eql(10)
  expect(res.body.profile.vote).to.eql({ totalCount: 8, mbti: { ESFP: 3 }, enneagram: { '1w2': 3 }, horoscope: { Cancer: 1, Leo: 1, Virgo: 1, Aries: 2 } })


  profile.birthday = new Date('2024-12-29')
  await profile.save()
  await commentsUpdateDelete([profile._id])
  await databaseLib.updateProfileDetailsForZodiacSignForPublicBirthDate(profile.id)

  res = await request(app)
  .get('/web/database/profile')
  .query({ id: 4 });
  expect(res.status).to.equal(200);
  expect(res.body.profile.horoscope).to.eql('Capricorn')
  expect(res.body.profile.confidence_score.horoscope).to.eql(100)
  expect(res.body.profile.vote).to.eql({ totalCount: 8, mbti: { ESFP: 3 }, enneagram: { '1w2': 3 } })

  res = await request(app)
  .get('/v1/comment')
  .query({ profileId: profile._id.valueOf() })
  .set('authorization', 0);
  expect(res.status).to.equal(200);

  comments = res.body.comments.map(x => {
    const result = {};
    if (x.vote) {
      result.vote = x.vote;
    }
    if (x.text || x.text == '') {
      result.text = x.text;
    }
    if (x.gif) {
      result.gif = x.gif;
    }
    return result;
  });

  expect(comments).to.eql([
    { 'text': '', gif: 'https://media.tenor.com/4w937X8-7aUAAAAM/mcdonalds-grimace.gif' },
    { vote: { enneagram: '1w2' }, 'text': '', gif: 'https://media.tenor.com/4w937X8-7aUAAAAM/mcdonalds-grimace.gif' },
    { 'text': 'check' },
    { vote: { mbti: 'ESFP'}, 'text': '' },
    { vote: { enneagram: '1w2' }, 'text': '' },
    { vote: { mbti: 'ESFP', enneagram: '1w2' }, 'text': '' },
    { vote: { mbti: 'ESFP' }, 'text': '' }
  ]);
  });
})

describe('social proof sign up', async () => {

  it('get recent sign up 5 user pics', async () => {
    basic.assignConfig.restore();
    sinon.stub(basic, 'assignConfig').returns(true);

    const sleep = (ms) => new Promise((resolve) => setTimeout(resolve, ms));

    const numUser = 5
    let usersPictures = []
    for(let i = 0; i < numUser; i++ ){
      res = await request(app)
        .put('/v1/user/initApp')
        .set('authorization', i)
        .send({ appVersion: '1.13.70'});
      expect(res.status).to.equal(200);

      res = await request(app)
      .post('/v1/user/picture/v2')
      .set('authorization', i)
      .attach('image', validImagePath);
      expect(res.status).to.equal(200);

      user = await User.findOne({ _id: i });
      console.log('user pictures :', user.pictures)
      console.log('user createdAt :', user.createdAt)
      expect(user.pictures.length).to.equal(1);
      usersPictures.push(user.pictures[0])


      await sleep(20);
    }

    usersPictures.reverse()

    res = await request(app)
      .get('/web/recentSignups');
    expect(res.status).to.equal(200);
    console.log('res.body :', res.body)
    expect(res.body.images).to.deep.equal(usersPictures)

    //second call chould be from cache
    res = await request(app)
      .get('/web/recentSignups');
    expect(res.status).to.equal(200);
    console.log('res.body :', res.body)
    expect(res.body.images).to.deep.equal(usersPictures)
  })

})

it('linked keywords linkedCategories linkedSubcategories linkedProfiles for profiles', async () => {
  const profiles = [
    {
      id: 100, sort: 5, name: 'Wayne Rooney', slug:'wayne-rooney', mbti: 'INFJ', subcategories: [3], enneagram: '1w2', horoscope: 'Taurus', image: 'pikachu.jpg', countries: ['England'], intros: { en: { intro: 'Wayne Rooney played with zidane watched pokemon in tv, played as striker'}}
    },
    {
      id: 101, sort: 5, name: 'Zidane', slug:'zidane', mbti: 'INFJ', subcategories: [3], enneagram: '1w2', horoscope: 'Taurus', image: 'pikachu.jpg', countries: ['France']
    },
    {
      id: 102, sort: 5, name: 'Wayne Rooney a b c', slug:'wayne-rooney-a-b-c', mbti: 'INFJ', subcategories: [3], enneagram: '1w2', horoscope: 'Taurus', image: 'pikachu.jpg', countries: ['England'], intros: { en: { intro: 'Wayne Rooney played with zidane watched pokemon in tv, played as striker'}}
    },
    {
      id: 103, sort: 5, name: 'Wayne Rooney a b', slug:'wayne-rooney-a-b', mbti: 'INFJ', subcategories: [3], enneagram: '1w2', horoscope: 'Taurus', image: 'pikachu.jpg', countries: ['England'], intros: { en: { intro: 'Wayne Rooney played with zidane watched pokemon in tv, played as striker'}}
    },
    {
      id: 104, sort: 5, name: 'wayne rooney a b c d e f g h i j k', slug:'wayne-rooney-a-b-c-d-e-f-g-h-i-j-k', mbti: 'INFJ', subcategories: [3], enneagram: '1w2', horoscope: 'Taurus', image: 'pikachu.jpg', countries: ['England'], intros: { en: { intro: 'Wayne Rooney played with zidane watched pokemon in tv, played as striker'}}
    },
    {
      id: 105, sort: 5, name: 'Wayne Rooney a b c duplicated', slug:'wayne-rooney-a-b', mbti: 'INFJ', subcategories: [3], enneagram: '1w2', horoscope: 'Taurus', image: 'pikachu.jpg', countries: ['England'], intros: { en: { intro: 'Wayne Rooney played with zidane watched pokemon in tv, played as striker'}}
    },
  ]

  await Profile.insertMany(profiles)

  const categories = [
    {
      id: 3, sort: 1, name: 'Anime', slug: 'anime',
    },
    {
      id: 4, sort: 2, name: 'TV', slug: 'tv',
    },
  ];
  await Category.insertMany(categories);

  const subcategories = [
    {
      id: 1, sort: 1, name: 'striker', slug: 'striker', category: 4,
    },
    {
      id: 2, sort: 2, name: 'midfilder', slug: 'midfilder', category: 4,
    },
  ];
  await Subcategory.insertMany(subcategories);

    let profileData = await Profile.findOne({ slug: 'wayne-rooney-a-b', duplicated: true });
    expect(profileData).to.eql(null);

  await databaseLib.downloadCategoriesToFiles();
  await databaseLib.loadCategoriesFromDatabase();
  await databaseLib.markDuplicateProfilesBySlugs();

  profileData = await Profile.findOne({ slug: 'wayne-rooney-a-b', duplicated: true });
  expect(profileData).to.not.eql(null);

  profileData = await Profile.findOne({ id: 100 })
  expect(profileData.linkedCategories).to.eql([]);
  expect(profileData.linkedSubcategories).to.eql([]);
  expect(profileData.linkedProfiles).to.eql([]);
  expect(profileData.linkedKeywordsUpdatedAt).to.eql();
  expect(profileData.linkedKeywordsProcessingStartAt).to.eql();

  res = await request(app)
  .get('/web/database/profile')
  .query({ id: 100, locale: 'en' });
  expect(res.status).to.equal(200);
  expect(res.body.profile.linkedCategories).to.eql([]);
  expect(res.body.profile.linkedSubcategories).to.eql([]);
  expect(res.body.profile.linkedProfiles).to.eql([]);

  profileData = await Profile.findOne({ id: 100 }).lean()
  expect(profileData.linkedCategories).to.eql([]);
  expect(profileData.linkedSubcategories).to.eql([]);
  expect(profileData.linkedProfiles).to.eql([]);

  await new Promise((resolve) => setTimeout(resolve, 50));

  res = await request(app)
  .get('/web/database/profile')
  .query({ id: 100, locale: 'en' });
  expect(res.status).to.equal(200);
  expect(res.body.profile.linkedCategories).to.eql([ { id: 4, slug: 'tv' } ]);
  expect(res.body.profile.linkedProfiles).to.eql([ { id: 101, slug: 'zidane' } ]);
  expect(res.body.profile.linkedSubcategories).to.eql([ { id: 1, slug: 'striker', categoryId: 4 }]);

  profileData = await Profile.findOne({ id: 100 }).lean()
  expect(profileData.linkedKeywordsProcessingStartAt).to.eql(null);
  expect(profileData.linkedKeywordsUpdatedAt).to.be.a('date');
  let linkedKeywordsUpdatedAt = profileData.linkedKeywordsUpdatedAt

  res = await request(app)
  .get('/web/database/profile')
  .query({ id: 100, locale: 'en' });
  expect(res.status).to.equal(200);

  await new Promise((resolve) => setTimeout(resolve, 50));

  profileData = await Profile.findOne({ id: 100 }).lean()
  expect(profileData.linkedKeywordsProcessingStartAt).to.eql(null);
  expect(profileData.linkedKeywordsUpdatedAt).to.be.a('date');
  expect(new Date(profileData.linkedKeywordsUpdatedAt)).to.eql(new Date(linkedKeywordsUpdatedAt));

  profile = await Profile.findOne({ id: 100 })
  profile.intros = {
    en: {
      intro: 'Wayne Rooney played with zidane watched pokemon in tv, played as striker and midfilder wayne rooney a b c d e f g h i j k and t',
      enneagram: ''
    }
  }
  await profile.save()

  res = await request(app)
  .get('/web/database/profile')
  .query({ id: 100, locale: 'en' });
  expect(res.status).to.equal(200);

  await new Promise((resolve) => setTimeout(resolve, 50));

  profileData = await Profile.findOne({ id: 100 }).lean()
  expect(profileData.linkedKeywordsProcessingStartAt).to.eql(null);
  expect(profileData.linkedKeywordsUpdatedAt).to.be.a('date');
  expect(new Date(profileData.linkedKeywordsUpdatedAt)).to.eql(new Date(linkedKeywordsUpdatedAt));
  expect(profileData.linkedCategories).to.eql([ { id: 4, slug: 'tv' } ]);
  expect(profileData.linkedProfiles).to.eql([ { id: 101, slug: 'zidane' } ]);
  expect(profileData.linkedSubcategories).to.eql([ { id: 1, slug: 'striker', categoryId: 4 } ]);

  // // 100 day
  let clock = sinon.useFakeTimers(Date.now());
  clock.tick(100 * 24 * 3600 * 1000);

  res = await request(app)
  .get('/web/database/profile')
  .query({ id: 100, locale: 'en' });
  expect(res.status).to.equal(200);

  clock.restore();

  await new Promise((resolve) => setTimeout(resolve, 50));

  profileData = await Profile.findOne({ id: 100 }).lean()
  expect(profileData.linkedKeywordsProcessingStartAt).to.eql(null);
  expect(profileData.linkedKeywordsUpdatedAt).to.be.a('date');
  expect(new Date(profileData.linkedKeywordsUpdatedAt)).to.gt(new Date(linkedKeywordsUpdatedAt));
  expect(profileData.linkedCategories).to.eql([ { id: 4, slug: 'tv' } ]);
  expect(profileData.linkedProfiles).to.eql([
    { id: 101, slug: 'zidane' },
    { id: 102, slug: 'wayne-rooney-a-b-c' },
    { id: 103, slug: 'wayne-rooney-a-b' },
   ]);
  expect(profileData.linkedSubcategories).to.eql([ { id: 2, slug: 'midfilder', categoryId: 4 }, { id: 1, slug: 'striker', categoryId: 4 } ]);

});

it('updateAutoIndexNowSitemap from google sheet', async () => {
  const res = await request(app)
    .post('/v1/worker/updateAutoIndexNowSitemap')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
})

describe('migrate web queries to backend', async () => {
  it('database page popular profiles category', async () => {

    const categories = [
      {
        id: 2, sort: 1, name: 'Anime', slug: 'anime',translatedLanguages:['en','es']
      },
      {
        id: 4, sort: 2, name: 'TV', slug: 'tv',translatedLanguages:['es']
      },
    ];
    await Category.insertMany(categories);

    const subcategories = [
      {
        id: 1, sort: 1, name: 'Naruto', slug: 'naruto', category: 2, countries: ['Chile'],
      },
      {
        id: 2, sort: 2, name: 'Bleach', slug: 'bleach', category: 2, countries: ['Chile'],
      },
      {
        id: 3, sort: 3, name: 'Pokemon', slug: 'pokemon', category: 2, translatedLanguages:['es'], countries: ['Chile'],
      },
      {
        id: 4, sort: 4, name: 'Manifest', slug: 'manifest', category: 4, translatedLanguages:['en','fr','es'], countries: ['Chile', 'Argentina', 'Bolivia'],
      },
      {
        id: 5, sort: 4, name: 'Sports', slug: 'Sports', category: 4, countries: ['Chile', 'Argentina', 'Bolivia', 'Colombia'],
      },
    ];
    await Subcategory.insertMany(subcategories);

    const profiles = [
      {
        id: 0, sort: 2, name: 'Bleach 0', mbti: 'ENTP', subcategories: [12], enneagram: '1w9', horoscope: 'Virgo',
      },
      {
        id: 1, sort: 5, name: 'Pikachu', mbti: 'ENTP', subcategories: [3], enneagram: '1w2', horoscope: 'Aries', image: 'pikachu.jpg',
      },
      {
        id: 2, sort: 3, name: 'Sasuke Uchiha', mbti: 'ENTP', subcategories: [3,4], countries: ['Chile', 'Argentina', 'Bolivia', 'Colombia'], enneagram: '1w9', horoscope: 'Taurus',
      },
      {
        id: 3, sort: 4, name: 'Sakura', slug: 'sakura', mbti: 'ENTJ', subcategories: [3], countries: ['Chile', 'Argentina', 'Bolivia', 'Colombia'], image: 'sakura.jpg',
      },
      {
        id: 4, sort: 2, name: 'Bleach', mbti: 'ENTJ', subcategories: [3,12], enneagram: '1w9', horoscope: 'Aries',
      },
      {
        id: 5, sort: 5, name: 'Naruto', mbti: 'ESTJ', subcategories: [11], enneagram: '1w9', horoscope: 'Aries',translatedLanguages:['fr']
      },
      {
        id: 6, sort: 5, name: 'Manifest', mbti: 'ENTP', enneagram: '1w2', countries: ['Chile', 'Argentina', 'Bolivia', 'Colombia'], subcategories: [4],
      },
      {
        id: 7, sort: 6, name: 'Manifest', mbti: 'ENTP', enneagram: '1w2', subcategories: [5], horoscope: 'Taurus'
      },
      {
        id: 8, sort: 2, name: 'Bleach 1', mbti: 'ENTJ', subcategories: [12], enneagram: '1w9', horoscope: 'Aries',
      },
      {
        id: 9, sort: 2, name: 'Bleach 2', mbti: 'ENTJ', subcategories: [12], enneagram: '1w9', horoscope: 'Aries',
      },
      {
        id: 10, sort: 2, name: 'Bleach 3', mbti: 'ENTP', subcategories: [12], enneagram: '1w2', horoscope: 'Virgo',
      },
      {
        id: 11, sort: 2, name: 'Bleach 4', mbti: 'ENTP', subcategories: [12], enneagram: '1w2', horoscope: 'Virgo',
      },
      {
        id: 12, sort: 2, name: 'Bleach 5', mbti: 'ENTP', subcategories: [12], enneagram: '1w2', horoscope: 'Virgo',
      },
      {
        id: 13, sort: 2, name: 'Bleach 6', mbti: 'ENTJ', subcategories: [12], enneagram: '1w2', horoscope: 'Virgo',
      },
      {
        id: 14, sort: 2, name: 'Bleach 7', mbti: 'ENTP', subcategories: [12], enneagram: '1w9', horoscope: 'Virgo',
      },
      {
        id: 15, sort: 2, name: 'Bleach 8', mbti: 'ENTP', subcategories: [12], enneagram: '1w2', horoscope: 'Aries',
      },
    ];

    await Profile.insertMany(profiles)
    await databaseLib.backFillPersonalityData();
    await databaseLib.downloadCategoriesToFiles();
    await databaseLib.loadCategoriesFromDatabase();
    await sitemapPaginationLib.createSitemapPaginatedForProfiles()
    await sitemapPaginationLib.createSitemapPaginationForCategory()
    await sitemapPaginationLib.createSitemapPaginationForSubcategory()

    let res = await request(app)
      .get('/web/cached/profiles/database')
    expect(res.status).to.equal(200);
    expect(res.body.categories['2'].length).to.equal(4)
    expect(res.body.categories['2'][0].id).to.equal(1)
    expect(res.body.categories['2'][1].id).to.equal(3)
    expect(res.body.categories['2'][2].id).to.equal(2)
    expect(res.body.categories['2'][3].id).to.equal(4)

    expect(res.body.categories['4'].length).to.equal(2)
    expect(res.body.categories['4'][0].id).to.equal(7)
    expect(res.body.categories['4'][1].id).to.equal(6)


    res = await request(app)
      .get('/web/cached/profiles/database')
      .query({ locale: 'es' })
    expect(res.status).to.equal(200);

    // category 2 changes with locale
    expect(res.body.categories['2'].length).to.equal(2)
    expect(res.body.categories['2'][0].id).to.equal(3)
    expect(res.body.categories['2'][1].id).to.equal(2)

    // category 3, 7, 8, 5, 4 doesnt change with locale
    expect(res.body.categories['4'].length).to.equal(2)
    expect(res.body.categories['4'][0].id).to.equal(7)
    expect(res.body.categories['4'][1].id).to.equal(6)

    res = await request(app)
      .get('/web/cached/profiles/database')
      .query({ locale: 'es', filter: 'aries' })
    expect(res.status).to.equal(200);

    expect(res.body.categories['2'].length).to.equal(0)
    expect(res.body.categories['4'].length).to.equal(0)

    res = await request(app)
      .get('/web/cached/profiles/database')
      .query({ locale: 'es', filter: 'taurus' })
    expect(res.status).to.equal(200);

    expect(res.body.categories['2'].length).to.equal(1)
    expect(res.body.categories['2'][0].id).to.equal(2)

    expect(res.body.categories['4'].length).to.equal(1)
    expect(res.body.categories['4'][0].id).to.equal(7)

    res = await request(app)
      .get('/web/cached/profiles/database')
      .query({ locale: 'es', type:'famous' })
    expect(res.status).to.equal(200);

    expect(res.body.categories['2'].length).to.equal(2)
    expect(res.body.categories['2'][0].id).to.equal(3)
    expect(res.body.categories['2'][1].id).to.equal(2)

    res = await request(app)
      .get('/web/cached/profiles/database')
      .query({ locale: 'es', type:'fictional' })
    expect(res.status).to.equal(200);

    expect(res.body.categories['4'].length).to.equal(3)
    expect(res.body.categories['4'][0].id).to.equal(7)
    expect(res.body.categories['4'][1].id).to.equal(6)
    expect(res.body.categories['4'][2].id).to.equal(2)

    res = await request(app)
      .get('/web/cached/profiles/category')
      .query({ locale: 'es', categoryId:'17' })
    expect(res.status).to.equal(422);

    res = await request(app)
      .get('/web/cached/profiles/category')
      .query({ locale: 'es', categoryId:'2' })
    expect(res.status).to.equal(200);

    expect(res.body.profiles.length).to.equal(2) // availableCountries used filter with chile
    expect(res.body.profiles[0].id).to.equal(3)
    expect(res.body.profiles[1].id).to.equal(2)

    res = await request(app)
      .get('/web/cached/profiles/category')
      .query({ locale: 'es', categoryId:'4' })
    expect(res.status).to.equal(200);

    expect(res.body.profiles.length).to.equal(3) // availableCountries forced not used
    expect(res.body.profiles[0].id).to.equal(7)
    expect(res.body.profiles[1].id).to.equal(6)
    expect(res.body.profiles[2].id).to.equal(2)

    res = await request(app)
      .get('/web/cached/profiles/subcategory')
      .query({ locale: 'es', subCategoryId:'2000' })
    expect(res.status).to.equal(422);

    res = await request(app)
      .get('/web/cached/profiles/subcategory')
      .query({ locale: 'es', subCategoryId:'3' })
    expect(res.status).to.equal(200);

    expect(res.body.hasMore).to.equal(false)
    expect(res.body.profiles.length).to.equal(2) // availableCountries used filter with chile
    expect(res.body.profiles[0].id).to.equal(3)
    expect(res.body.profiles[1].id).to.equal(2)

    // test pagination and hasMore
    res = await request(app)
      .get('/web/cached/profiles/subcategory')
      .query({ locale: 'es', subCategoryId:'3', size: 1 })
    expect(res.status).to.equal(200);

    expect(res.body.hasMore).to.equal(true)
    expect(res.body.profiles.length).to.equal(1)
    expect(res.body.profiles[0].id).to.equal(3)

    res = await request(app)
      .get('/web/cached/profiles/subcategory')
      .query({ locale: 'es', subCategoryId:'3', size: 1, page: 2 })
    expect(res.status).to.equal(200);

    expect(res.body.hasMore).to.equal(false)
    expect(res.body.profiles.length).to.equal(1)
    expect(res.body.profiles[0].id).to.equal(2)

    res = await request(app)
      .get('/web/cached/profiles/subcategory')
      .query({ locale: 'es', subCategoryId:'5' })
    expect(res.status).to.equal(200);

    expect(res.body.profiles.length).to.equal(1)
    expect(res.body.profiles[0].id).to.equal(7)

    res = await request(app)
      .get('/web/cached/profiles/profile')
      .query({ locale: 'es', profileId: '2000' })
    expect(res.status).to.equal(422);

    res = await request(app)
      .get('/web/cached/profiles/profile')
      .query({ locale: 'es', profileId: '4' })
    expect(res.status).to.equal(200);

    expect(res.body.mbtiProfiles.map(x=>x.id)).to.have.members([3])
    expect(res.body.enneagramProfiles.map(x=>x.id)).to.have.members([2])
    expect(res.body.horoscopeProfiles.map(x=>x.id)).to.have.members([1])
    expect(res.body.relatedProfiles.map(x=>x.id)).to.have.members([8, 9, 10, 11, 12, 13, 14, 15, 0])

    let profile = await Profile.findOne({ id: 4 })
    expect(profile.horoscope).to.equal('Aries');
    expect(profile.detailPageProfilesCache.mbtiProfiles).to.have.members([3])
    expect(profile.detailPageProfilesCache.enneagramProfiles).to.have.members([2])
    expect(profile.detailPageProfilesCache.horoscopeProfiles).to.have.members([1])
    expect(profile.detailPageProfilesCache.relatedProfiles).to.have.members([8, 9, 10, 11, 12, 13, 14, 15, 0])

    res = await request(app)
      .get('/web/cached/profiles/profile')
      .query({ locale: 'fr', profileId: '4' })
    expect(res.status).to.equal(200);

    expect(res.body.mbtiProfiles.map(x=>x.id)).to.have.members([3])
    expect(res.body.enneagramProfiles.map(x=>x.id)).to.have.members([2])
    expect(res.body.horoscopeProfiles.map(x=>x.id)).to.have.members([1])
    expect(res.body.relatedProfiles.map(x=>x.id)).to.have.members([8, 9, 10, 11, 12, 13, 14, 15, 0])

    profile = await Profile.findOne({ id: 4 })
    profile.linkedProfiles = [{
      id: 8, slug: 'bleach-1'
    }]
    profile.detailPageProfilesCache = undefined
    await profile.save()

    res = await request(app)
      .get('/web/cached/profiles/profile')
      .query({ locale: 'fr', profileId: '4' })
    expect(res.status).to.equal(200);

    expect(res.body.mbtiProfiles.map(x=>x.id)).to.have.members([3]) // 8 will not be inclded in the result as its already in linkedProfiles
    expect(res.body.enneagramProfiles.map(x=>x.id)).to.have.members([2])
    expect(res.body.horoscopeProfiles.map(x=>x.id)).to.have.members([1])
    expect(res.body.relatedProfiles.map(x=>x.id)).to.have.members([8, 9, 10, 11, 12, 13, 14, 15, 0])


    profile = await Profile.findOne({ id: 4 })
    profile.horoscope = 'Virgo'
    await profile.save()

    profile = await Profile.findOne({ id: 4 })
    expect(profile.horoscope).to.equal('Virgo');
    expect(profile.detailPageProfilesCache.mbtiProfiles).to.have.members([])
    expect(profile.detailPageProfilesCache.enneagramProfiles).to.have.members([])
    expect(profile.detailPageProfilesCache.horoscopeProfiles).to.have.members([])
    expect(profile.detailPageProfilesCache.relatedProfiles).to.have.members([])

    res = await request(app)
      .get('/web/cached/profiles/profile')
      .query({ locale: 'es', profileId: '4' })
    expect(res.status).to.equal(200);

    expect(res.body.mbtiProfiles.map(x=>x.id)).to.have.members([3])
    expect(res.body.enneagramProfiles.map(x=>x.id)).to.have.members([2])
    expect(res.body.horoscopeProfiles.map(x=>x.id)).to.have.members([])
    expect(res.body.relatedProfiles.map(x=>x.id)).to.have.members([8, 9, 10, 11, 12, 13, 14, 15, 0, 1])

    profile = await Profile.findOne({ id: 4 })
    expect(profile.horoscope).to.equal('Virgo');
    expect(profile.detailPageProfilesCache.mbtiProfiles).to.have.members([3])
    expect(profile.detailPageProfilesCache.enneagramProfiles).to.have.members([2])
    expect(profile.detailPageProfilesCache.horoscopeProfiles).to.have.members([])
    expect(profile.detailPageProfilesCache.relatedProfiles).to.have.members([8, 9, 10, 11, 12, 13, 14, 15, 0, 1])

    res = await request(app)
      .get('/web/cached/profiles/profile')
      .query({ locale: 'fr', profileId: '4' })
    expect(res.status).to.equal(200);

    expect(res.body.mbtiProfiles.map(x=>x.id)).to.have.members([3])
    expect(res.body.enneagramProfiles.map(x=>x.id)).to.have.members([2])
    expect(res.body.horoscopeProfiles.map(x=>x.id)).to.have.members([])
    expect(res.body.relatedProfiles.map(x=>x.id)).to.have.members([8, 9, 10, 11, 12, 13, 14, 15, 0, 1])

    await Subcategory.insertMany([
      {
        id: 200000, sort: 1, name: 'Naruto-222', slug: 'naruto-222', category: 3,
      },
    ])

    res = await request(app)
      .get('/web/cached/profiles/subcategory')
      .query({ locale: 'es', subCategoryId:'200000' })
    expect(res.status).to.equal(200);

    // adding test for /web/cached/profiles/subcategory/sitemap
    let sitemapData = await SitemapPaginatedSubcategories.find({ subcategoryId: 3 }).lean()
    expect(sitemapData.length).to.equal(2)
    expect(sitemapData[0].totalPages).to.equal(2)
    expect(sitemapData[0].profilesId).to.eql([4, 1])
    expect(sitemapData[0].pageNo).to.equal(1)
    expect(sitemapData[1].profilesId).to.eql([3, 2])
    expect(sitemapData[1].pageNo).to.equal(2)
    expect(sitemapData[1].totalPages).to.equal(2)

    res = await request(app)
      .get('/web/cached/profiles/subcategory/sitemap')
      .query({ subCategoryId:'200000' })
    expect(res.status).to.equal(422);

    res = await request(app)
      .get('/web/cached/profiles/subcategory/sitemap')
      .query({ subCategoryId: 3 })
    expect(res.status).to.equal(200);
    expect(res.body.totalPages).to.equal(2)
    expect(res.body.profiles.length).to.equal(2)
    expect(res.body.profiles[0].id).to.equal(4)
    expect(res.body.profiles[0].name).to.equal('Bleach')
    expect(res.body.profiles[1].id).to.equal(1)
    expect(res.body.profiles[1].name).to.equal('Pikachu')

    res = await request(app)
      .get('/web/cached/profiles/subcategory/sitemap')
      .query({ subCategoryId: 3, page: 2 })
    expect(res.status).to.equal(200);
    expect(res.body.totalPages).to.equal(2)
    expect(res.body.profiles.length).to.equal(2)
    expect(res.body.profiles[0].id).to.equal(3)
    expect(res.body.profiles[0].name).to.equal('Sakura')
    expect(res.body.profiles[1].id).to.equal(2)
    expect(res.body.profiles[1].name).to.equal('Sasuke Uchiha')

    res = await request(app)
      .get('/web/cached/profiles/subcategory/sitemap')
      .query({ subCategoryId: 3, page: 3 }) //page 3 dont exist
    expect(res.status).to.equal(200);
    expect(res.body.totalPages).to.equal(2)
    expect(res.body.profiles.length).to.equal(0)

    // adding test for /web/cached/profiles/sitemap
    sitemapData = await SitemapPaginatedCategories.find({ categoryId: 2 }).lean()
    expect(sitemapData.length).to.equal(2)
    expect(sitemapData[0].totalPages).to.equal(2)
    expect(sitemapData[0].subcategoriesId).to.eql([2, 1])
    expect(sitemapData[0].pageNo).to.equal(1)
    expect(sitemapData[1].subcategoriesId).to.eql([3])
    expect(sitemapData[1].pageNo).to.equal(2)
    expect(sitemapData[1].totalPages).to.equal(2)

    res = await request(app)
      .get('/web/cached/profiles/category/sitemap')
      .query({ categoryId: 2 })
    expect(res.status).to.equal(200);
    expect(res.body.totalPages).to.equal(2)
    expect(res.body.subcategories.length).to.equal(2)
    expect(res.body.subcategories[0].id).to.equal(2)
    expect(res.body.subcategories[0].name).to.equal('Bleach')
    expect(res.body.subcategories[1].id).to.equal(1)
    expect(res.body.subcategories[1].name).to.equal('Naruto')

    res = await request(app)
      .get('/web/cached/profiles/category/sitemap')
      .query({ categoryId: 2, page: 2 })
    expect(res.status).to.equal(200);
    expect(res.body.totalPages).to.equal(2)
    expect(res.body.subcategories.length).to.equal(1)
    expect(res.body.subcategories[0].id).to.equal(3)
    expect(res.body.subcategories[0].name).to.equal('Pokemon')

    res = await request(app)
      .get('/web/cached/profiles/category/sitemap')
      .query({ categoryId: 2, page: 5 })
    expect(res.status).to.equal(200);
    expect(res.body.totalPages).to.equal(2)
    expect(res.body.subcategories.length).to.equal(0)

    // add test for /web/cached/profiles/sitemap
    sitemapData = await SitemapPaginatedProfiles.find({}).lean()
    expect(sitemapData.length).to.equal(8)
    expect(sitemapData[0].totalPages).to.equal(8)
    expect(sitemapData[0].startId).to.equal(0)
    expect(sitemapData[0].pageNo).to.equal(1)
    expect(sitemapData[1].startId).to.equal(2)
    expect(sitemapData[1].pageNo).to.equal(2)
    expect(sitemapData[1].totalPages).to.equal(8)
    expect(sitemapData[7].startId).to.equal(14)
    expect(sitemapData[7].pageNo).to.equal(8)
    expect(sitemapData[7].totalPages).to.equal(8)

    res = await request(app)
      .get('/web/cached/profiles/sitemap')
      .query({ page: 1 })
    expect(res.status).to.equal(200);
    expect(res.body.totalPages).to.equal(8)
    expect(res.body.profiles.length).to.equal(2)
    expect(res.body.profiles[0].id).to.equal(0)
    expect(res.body.profiles[1].id).to.equal(1)

    res = await request(app)
      .get('/web/cached/profiles/sitemap')
      .query({ page: 2 })
    expect(res.status).to.equal(200);
    expect(res.body.totalPages).to.equal(8)
    expect(res.body.profiles.length).to.equal(2)
    expect(res.body.profiles[0].id).to.equal(2)
    expect(res.body.profiles[1].id).to.equal(3)
    expect(res.body.profiles[1].subcategories.length).to.eql(1)
    expect(res.body.profiles[1].categories.length).to.eql(1)
    expect(res.body.profiles[1].url).to.equal('/database/profile/3/sakura-personality-type')

    res = await request(app)
      .get('/web/cached/profiles/sitemap')
      .query({ page: 6 })
    expect(res.status).to.equal(200);
    expect(res.body.totalPages).to.equal(8)
    expect(res.body.profiles.length).to.equal(2)
    expect(res.body.profiles[0].id).to.equal(10)
    expect(res.body.profiles[1].id).to.equal(11)

    res = await request(app)
      .get('/web/cached/profiles/sitemap')
      .query({ page: 9 })
    expect(res.status).to.equal(200);
    expect(res.body.totalPages).to.equal(8)
    expect(res.body.profiles.length).to.equal(0)

  })

  it('category info API', async () => {
    const categories = [
      {
        id: 100,
        name: 'Celebrities',
        slug: 'celebrities',
        numProfiles: 100,
        translatedLanguages: ['en', 'es'],
        personalityCount: {
          mbti: { ENFP: 25, INTJ: 15 },
          enneagram: { '8w7': 20, '5w6': 10 },
          horoscope: { Leo: 30, Scorpio: 5 }
        },
        numSubcategories: 5,
        intros: {
          en: {
            enfp: 'This is an intro for ENFPs in celebrities category'
          },
          es: {
            enfp: 'Esta es una introducción para ENFPs en la categoría de celebridades'
          }
        },
        linkedPillarKeywords: {
          en: {
            enfp: [{ keyword: 'personality', url: '/personality-database' }]
          },
          es: {
            enfp: [{ keyword: 'personalidad', url: '/personality-database' }]
          }
        }
      }
    ];

    await Category.insertMany(categories);

    let res = await request(app)
      .get('/web/category')
      .query({ slug: 'celebrities', filter: 'enfp', locale: 'en', country: 'undefined' });
    expect(res.status).to.equal(200);
    expect(res.body.category.id).to.equal(100);
    expect(res.body.category.name).to.equal('Celebrities');
    expect(res.body.category.slug).to.equal('celebrities');
    expect(res.body.category.numProfiles).to.equal(100);
    expect(res.body.category.numSubcategories).to.equal(5);
    expect(res.body.category.translatedLanguages).to.deep.equal(['en', 'es']);
    expect(res.body.category.personalityCount).to.eql({
      mbti: { ENFP: 25, INTJ: 15 },
      enneagram: { '8w7': 20, '5w6': 10 },
      horoscope: { Leo: 30, Scorpio: 5 }
    });
    expect(res.body.category.intros.en.enfp).to.include('This is an intro for ENFPs');
    expect(res.body.category.linkedPillarKeywords.en.enfp).to.eql([
      { keyword: 'personality', url: '/personality-database' }
    ]);

    res = await request(app)
      .get('/web/category')
      .query({ slug: 'celebrities', filter: 'enfp', locale: 'es' });
    expect(res.status).to.equal(200);
    expect(res.body.category.id).to.equal(100);
    expect(res.body.category.intros.en.enfp).to.include('This is an intro for ENFPs');
    expect(res.body.category.linkedPillarKeywords.en.enfp).to.eql([
      { keyword: 'personality', url: '/personality-database' }
    ]);
    expect(res.body.category.intros.es.enfp).to.include('Esta es una introducción para ENFPs');
    expect(res.body.category.linkedPillarKeywords.es.enfp).to.eql([
      { keyword: 'personalidad', url: '/personality-database' }
    ]);
  });

  it('subcategory info API', async () => {
    const categories = [
      {
        id: 1, sort: 1, name: 'Celebrities', slug: 'celebrities'
      }
    ];
    await Category.insertMany(categories);

    const subcategories = [
      {
        id: 100, sort: 1, name: 'Actors', slug: 'actors', category: 1,
        numProfiles: 50, personalityCount: { mbti: { infp: 10, enfp: 20 }, enneagram: { '1w9': 15, '2w1': 10 } },
        translatedLanguages: ['en', 'es'], countries: ['Japan', 'Nepal'], oldSlugs: ['old-actors-1', 'old-actors-2']
      }
    ];
    await Subcategory.insertMany(subcategories);

    let res = await request(app)
      .get('/web/subcategory')
      .query({ category: '1', slug: 'actors' });
    expect(res.status).to.equal(200);
    expect(res.body.subcategory.id).to.equal(100);
    expect(res.body.subcategory.name).to.equal('Actors');
    expect(res.body.subcategory.slug).to.equal('actors');
    expect(res.body.subcategory.category).to.equal(1);
    expect(res.body.subcategory.numProfiles).to.equal(50);
    expect(res.body.subcategory.translatedLanguages).to.deep.equal(['en', 'es']);
    expect(res.body.subcategory.countries).to.deep.equal(['Japan', 'Nepal']);
    expect(res.body.subcategory.oldSlugs).to.deep.eql(undefined);

    res = await request(app)
      .get('/web/subcategory')
      .query({ category: '1', slug: 'old-actors' });
    expect(res.status).to.equal(200);
    expect(res.body.subcategory).to.equal(null);

    res = await request(app)
      .get('/web/subcategory')
      .query({ category: '1', slug: 'old-actors-1' });
    expect(res.status).to.equal(200);
    expect(res.body.subcategory.id).to.equal(100);
    expect(res.body.subcategory.name).to.equal('Actors');
    expect(res.body.subcategory.slug).to.equal('actors');
    expect(res.body.subcategory.category).to.equal(1);
    expect(res.body.subcategory.numProfiles).to.equal(50);
    expect(res.body.subcategory.translatedLanguages).to.deep.equal(['en', 'es']);
    expect(res.body.subcategory.countries).to.deep.equal(['Japan', 'Nepal']);
    expect(res.body.subcategory.oldSlugs).to.deep.eql();
  });

  it('subcategory paragraph info API', async () => {
    const DatabaseParagraph = require('../models/database-paragraph');

    const paragraphs = [
      {
        type: 'subcategoryIntro',
        filter: 'famous',
        index: 1,
        translations: {
          en: {
            paragraph: 'This is an intro paragraph about [0:TYPE] [1:SUBCATEGORY] and [0:TYPE][1:SUBCATEGORY].',
            linkedPillarKeywords: [{ url: '/test', keyword: 'test' }]
          },
          bg: {
            paragraph: 'Това е въвеждащ параграф за [0:TYPE] [1:SUBCATEGORY] и [0:TYPE][1:SUBCATEGORY].',
            linkedPillarKeywords: [{ url: '/test', keyword: 'test' }]
          }
        }
      },
      {
        type: 'subcategoryOutro',
        filter: 'famous',
        index: 8,
        translations: {
          en: {
            paragraph: 'This is an outro paragraph about [1:SUBCATEGORY].',
            linkedPillarKeywords: []
          },
          bg: {
            paragraph: 'Това е заключителен параграф за [1:SUBCATEGORY].',
            linkedPillarKeywords: []
          }
        }
      }
    ];
    await DatabaseParagraph.insertMany(paragraphs);

    // Test Bulgarian locale
    const resBg = await request(app)
      .get('/web/cached/subcategory/paragraph')
      .query({
        type: 'famous',
        category: 'actors',
        subcategory: 'hollywood',
        locale: 'bg'
      });

    expect(resBg.status).to.equal(200);
    expect(resBg.body.subcategoryIntro.paragraph).to.equal('Това е въвеждащ параграф за [0:TYPE] [1:SUBCATEGORY] и [0:TYPE][1:SUBCATEGORY].');
    expect(resBg.body.subcategoryIntro.linkedPillarKeywords).to.eql([{ url: '/test', keyword: 'test' }]);
    expect(resBg.body.subcategoryOutro.paragraph).to.equal('Това е заключителен параграф за [1:SUBCATEGORY].');
    expect(resBg.body.subcategoryOutro.linkedPillarKeywords).to.eql([]);
    expect(resBg.body.personalityParagraph).to.eql({});
  });

  it('country paragraph info API', async () => {
    const DatabaseParagraph = require('../models/database-paragraph');

    const paragraphs = [
      {
        type: 'countryIntro',
        filter: 'famous',
        index: 2,
        translations: {
          bg: {
            paragraph: 'Въведение за държавата за [0:TYPE] [1:CATEGORY] от [2:COUNTRY].',
            linkedPillarKeywords: [{ url: '/country', keyword: 'country' }]
          }
        }
      },
      {
        type: 'countryParagraph',
        filter: 'Japan',
        index: 3,
        translations: {
          bg: {
            paragraph: 'Специфичен параграф за [3:NATIONALITY] хора.',
            linkedPillarKeywords: [{ url: '/nationality', keyword: 'nationality' }]
          }
        }
      },
      {
        type: 'countryOutro',
        filter: 'famous',
        index: 14,
        translations: {
          bg: {
            paragraph: 'Заключителен параграф за държавата.',
            linkedPillarKeywords: []
          }
        }
      }
    ];
    await DatabaseParagraph.insertMany(paragraphs);

    const res = await request(app)
      .get('/web/cached/country/paragraph')
      .query({
        type: 'famous',
        category: 'actors',
        subcategory: 'hollywood',
        locale: 'bg',
        country: 'Japan'
      });

    expect(res.status).to.equal(200);
    expect(res.body.countryIntro.paragraph).to.equal('Въведение за държавата за [0:TYPE] [1:CATEGORY] от [2:COUNTRY].');
    expect(res.body.countryIntro.linkedPillarKeywords).to.eql([{ url: '/country', keyword: 'country' }]);
    expect(res.body.countryParagraph.paragraph).to.equal('Специфичен параграф за [3:NATIONALITY] хора.');
    expect(res.body.countryParagraph.linkedPillarKeywords).to.eql([{ url: '/nationality', keyword: 'nationality' }]);
    expect(res.body.countryOutro.paragraph).to.equal('Заключителен параграф за държавата.');
    expect(res.body.countryOutro.linkedPillarKeywords).to.eql([]);
    expect(res.body.personalityParagraph).to.eql({});
  });
})
